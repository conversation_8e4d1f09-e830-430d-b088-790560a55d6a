#!/bin/bash
# Flink集群配置脚本

echo "=== 分布式Flink环境配置 ==="

# 1. 创建必要的目录
mkdir -p flink-cluster/{jobmanager,taskmanager1,taskmanager2,taskmanager3}
mkdir -p flink-cluster/config
mkdir -p flink-cluster/logs

# 2. 下载Flink（如果没有的话）
FLINK_VERSION="1.17.2"
FLINK_DIR="flink-${FLINK_VERSION}"

if [ ! -d "$FLINK_DIR" ]; then
    echo "下载Flink..."
    wget https://downloads.apache.org/flink/flink-${FLINK_VERSION}/flink-${FLINK_VERSION}-bin-scala_2.12.tgz
    tar -xzf flink-${FLINK_VERSION}-bin-scala_2.12.tgz
fi

# 3. 生成Flink配置文件
cat > flink-cluster/config/flink-conf.yaml << EOF
# Flink集群配置

# JobManager配置
jobmanager.rpc.address: hadoop01
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m
jobmanager.bind-host: 0.0.0.0

# TaskManager配置
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 2
taskmanager.bind-host: 0.0.0.0

# 高可用配置
high-availability: zookeeper
high-availability.zookeeper.quorum: hadoop01:2181,hadoop02:2181,hadoop03:2181
high-availability.zookeeper.path.root: /flink
high-availability.cluster-id: /default_ns
high-availability.storageDir: file:///tmp/flink/ha/

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file:///tmp/flink/checkpoints
state.savepoints.dir: file:///tmp/flink/savepoints

# 网络配置
rest.port: 8081
rest.bind-address: 0.0.0.0

# 并行度配置
parallelism.default: 3

# 重启策略
restart-strategy: fixed-delay
restart-strategy.fixed-delay.attempts: 3
restart-strategy.fixed-delay.delay: 10 s

# 日志配置
env.log.dir: ./flink-cluster/logs
EOF

# 4. 生成workers配置文件
cat > flink-cluster/config/workers << EOF
hadoop02
hadoop03
hadoop01
EOF

# 5. 生成masters配置文件
cat > flink-cluster/config/masters << EOF
hadoop01:8081
EOF

echo "Flink配置文件已生成完成！"
echo "配置文件位置："
echo "- 主配置: flink-cluster/config/flink-conf.yaml"
echo "- Workers: flink-cluster/config/workers"
echo "- Masters: flink-cluster/config/masters"

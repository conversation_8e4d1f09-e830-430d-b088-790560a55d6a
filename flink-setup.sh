#!/bin/bash
# Flink集群配置脚本

echo "=== 分布式Flink环境配置 ==="

# 1. 创建必要的目录
mkdir -p flink-cluster/{jobmanager,taskmanager1,taskmanager2,taskmanager3}
mkdir -p flink-cluster/config
mkdir -p flink-cluster/logs

# 2. 下载Flink（如果没有的话）
FLINK_VERSION="1.17.2"
FLINK_DIR="flink-${FLINK_VERSION}"

if [ ! -d "$FLINK_DIR" ]; then
    echo "下载Flink..."
    # 尝试多个镜像源
    FLINK_URL1="https://downloads.apache.org/flink/flink-${FLINK_VERSION}/flink-${FLINK_VERSION}-bin-scala_2.12.tgz"
    FLINK_URL2="https://archive.apache.org/dist/flink/flink-${FLINK_VERSION}/flink-${FLINK_VERSION}-bin-scala_2.12.tgz"
    FLINK_URL3="https://mirrors.tuna.tsinghua.edu.cn/apache/flink/flink-${FLINK_VERSION}/flink-${FLINK_VERSION}-bin-scala_2.12.tgz"

    # 尝试下载
    if wget -O flink-${FLINK_VERSION}-bin-scala_2.12.tgz "$FLINK_URL1" 2>/dev/null; then
        echo "从Apache官方源下载成功"
    elif wget -O flink-${FLINK_VERSION}-bin-scala_2.12.tgz "$FLINK_URL2" 2>/dev/null; then
        echo "从Apache归档源下载成功"
    elif wget -O flink-${FLINK_VERSION}-bin-scala_2.12.tgz "$FLINK_URL3" 2>/dev/null; then
        echo "从清华镜像源下载成功"
    else
        echo "所有下载源都失败，请手动下载Flink"
        exit 1
    fi

    # 验证下载完整性并解压
    if tar -tzf flink-${FLINK_VERSION}-bin-scala_2.12.tgz >/dev/null 2>&1; then
        echo "文件完整性验证通过，开始解压..."
        tar -xzf flink-${FLINK_VERSION}-bin-scala_2.12.tgz
        echo "Flink解压完成"
    else
        echo "下载的文件损坏，请重新运行脚本"
        rm -f flink-${FLINK_VERSION}-bin-scala_2.12.tgz
        exit 1
    fi
fi

# 3. 生成Flink配置文件
cat > flink-cluster/config/flink-conf.yaml << EOF
# Flink集群配置

# JobManager配置
jobmanager.rpc.address: hadoop01
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m
jobmanager.bind-host: 0.0.0.0

# TaskManager配置
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 2
taskmanager.bind-host: 0.0.0.0

# 高可用配置
high-availability: zookeeper
high-availability.zookeeper.quorum: hadoop01:2181,hadoop02:2181,hadoop03:2181
high-availability.zookeeper.path.root: /flink
high-availability.cluster-id: /default_ns
high-availability.storageDir: file:///tmp/flink/ha/

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file:///tmp/flink/checkpoints
state.savepoints.dir: file:///tmp/flink/savepoints

# 网络配置
rest.port: 8081
rest.bind-address: 0.0.0.0

# 并行度配置
parallelism.default: 3

# 重启策略
restart-strategy: fixed-delay
restart-strategy.fixed-delay.attempts: 3
restart-strategy.fixed-delay.delay: 10 s

# 日志配置
env.log.dir: ./flink-cluster/logs
EOF

# 4. 生成workers配置文件
cat > flink-cluster/config/workers << EOF
hadoop02
hadoop03
hadoop01
EOF

# 5. 生成masters配置文件
cat > flink-cluster/config/masters << EOF
hadoop01:8081
EOF

echo "Flink配置文件已生成完成！"
echo "配置文件位置："
echo "- 主配置: flink-cluster/config/flink-conf.yaml"
echo "- Workers: flink-cluster/config/workers"
echo "- Masters: flink-cluster/config/masters"

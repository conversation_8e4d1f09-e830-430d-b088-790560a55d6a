version: '3.8'

services:
  # Zookeeper for <PERSON><PERSON><PERSON>
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - event-driven-network

  # Kafka Broker
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      K<PERSON>KA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    networks:
      - event-driven-network

  # Flink JobManager
  flink-jobmanager:
    image: flink:1.17.2-scala_2.12
    hostname: flink-jobmanager
    container_name: flink-jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        parallelism.default: 2
        taskmanager.memory.process.size: 1728m
        taskmanager.numberOfTaskSlots: 2
    networks:
      - event-driven-network

  # Flink TaskManager
  flink-taskmanager:
    image: flink:1.17.2-scala_2.12
    hostname: flink-taskmanager
    container_name: flink-taskmanager
    depends_on:
      - flink-jobmanager
    command: taskmanager
    scale: 2
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: flink-jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 2
        taskmanager.memory.process.size: 1728m
    networks:
      - event-driven-network

  # MongoDB for Projections
  mongodb:
    image: mongo:6.0
    hostname: mongodb
    container_name: mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: ecommerce
    volumes:
      - mongodb_data:/data/db
    networks:
      - event-driven-network

  # Redis for Caching
  redis:
    image: redis:7.0-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - event-driven-network

  # Event Store API
  event-store-api:
    build:
      context: .
      dockerfile: Dockerfile.event-store
    hostname: event-store-api
    container_name: event-store-api
    ports:
      - "8080:8080"
    depends_on:
      - kafka
      - mongodb
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      MONGODB_URI: *****************************************************************
      REDIS_URL: redis://redis:6379
    networks:
      - event-driven-network

  # Recommendation Processor
  recommendation-processor:
    build:
      context: .
      dockerfile: Dockerfile.processor
    hostname: recommendation-processor
    container_name: recommendation-processor
    depends_on:
      - kafka
      - flink-jobmanager
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      FLINK_JOBMANAGER_ADDRESS: flink-jobmanager:8081
    networks:
      - event-driven-network

  # Event Generator (for demo)
  event-generator:
    build:
      context: .
      dockerfile: Dockerfile.generator
    hostname: event-generator
    container_name: event-generator
    depends_on:
      - kafka
    environment:
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      GENERATION_RATE: 10  # events per second
    networks:
      - event-driven-network

  # Kafka UI for monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    hostname: kafka-ui
    container_name: kafka-ui
    ports:
      - "8090:8080"
    depends_on:
      - kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - event-driven-network

  # Grafana for monitoring
  grafana:
    image: grafana/grafana:latest
    hostname: grafana
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - event-driven-network

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    hostname: prometheus
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - event-driven-network

volumes:
  mongodb_data:
  redis_data:
  grafana_data:
  prometheus_data:

networks:
  event-driven-network:
    driver: bridge

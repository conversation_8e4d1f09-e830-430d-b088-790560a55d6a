# 分布式实时电商推荐系统实验报告

## 目录

1. [分布式Kafka环境配置](#1-分布式kafka环境配置)
2. [分布式Flink环境配置](#2-分布式flink环境配置)
3. [实现推荐系统工程](#3-实现推荐系统工程)
4. [实现消息源软件工程](#4-实现消息源软件工程)
5. [总结与反思](#5-总结与反思)

## 1. 分布式Kafka环境配置

### 1.1 Kafka集群搭建

在本实验中，我搭建了一个由3个节点组成的Kafka集群，用于实现分布式消息传递系统。Kafka集群的配置如下：

- 3个Broker节点：broker-1、broker-2、broker-3
- 1个Zookeeper节点用于集群协调
- 复制因子设置为2，确保数据可靠性
- 分区数设置为3，提高并行处理能力

集群配置使用Docker Compose实现，配置文件如下：

```yaml
version: '3'
services:
  zookeeper:
    image: wurstmeister/zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    networks:
      - kafka-net

  kafka1:
    image: wurstmeister/kafka
    container_name: kafka1
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ADVERTISED_HOST_NAME: kafka1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_CREATE_TOPICS: "user-behaviors:3:2,recommendations:3:2"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - zookeeper
    networks:
      - kafka-net

  kafka2:
    image: wurstmeister/kafka
    container_name: kafka2
    ports:
      - "9093:9092"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ADVERTISED_HOST_NAME: kafka2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - zookeeper
    networks:
      - kafka-net

  kafka3:
    image: wurstmeister/kafka
    container_name: kafka3
    ports:
      - "9094:9092"
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ADVERTISED_HOST_NAME: kafka3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - zookeeper
    networks:
      - kafka-net

networks:
  kafka-net:
    driver: bridge
```

[此处插入截图：Kafka集群启动后的Docker容器状态]

### 1.2 Topic创建与配置

为了支持电商推荐系统的数据流，我创建了两个主题：

1. **user-behaviors**：用于存储用户行为数据
   - 分区数：3（提高并行处理能力）
   - 复制因子：2（确保数据可靠性）
   
2. **recommendations**：用于存储推荐结果数据
   - 分区数：3
   - 复制因子：2

Topic创建通过以下命令实现：

```bash
# 创建user-behaviors主题
docker exec kafka1 kafka-topics.sh --create --topic user-behaviors --partitions 3 --replication-factor 2 --bootstrap-server kafka1:9092

# 创建recommendations主题
docker exec kafka1 kafka-topics.sh --create --topic recommendations --partitions 3 --replication-factor 2 --bootstrap-server kafka1:9092
```

[此处插入截图：创建Topic的命令执行结果]

### 1.3 Kafka生产者实现

在消息源软件中，我实现了Kafka生产者，用于模拟用户行为数据的生成并发送到Kafka。生产者的核心代码如下：

```java
@Service
public class KafkaProducerService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    @Value("${kafka.topic.user-behaviors}")
    private String userBehaviorsTopic;
    
    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }
    
    public void sendUserBehavior(UserBehavior userBehavior) {
        try {
            String message = objectMapper.writeValueAsString(userBehavior);
            kafkaTemplate.send(userBehaviorsTopic, String.valueOf(userBehavior.getUserId()), message)
                .addCallback(
                    result -> log.info("User behavior sent: {}", userBehavior),
                    ex -> log.error("Failed to send user behavior", ex)
                );
        } catch (JsonProcessingException e) {
            log.error("Error serializing user behavior", e);
        }
    }
}
```

生产者配置：

```java
@Configuration
public class KafkaProducerConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.ACKS_CONFIG, "all");
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
```

[此处插入截图：Kafka生产者发送消息的日志]

### 1.4 Kafka消费者实现

我还实现了Kafka消费者，用于接收推荐系统生成的推荐结果：

```java
@Service
public class KafkaConsumerService {

    private final ObjectMapper objectMapper;
    private final List<Recommendation> latestRecommendations = new CopyOnWriteArrayList<>();
    
    public KafkaConsumerService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    @KafkaListener(topics = "${kafka.topic.recommendations}", groupId = "${spring.kafka.consumer.group-id}")
    public void consumeRecommendations(String message) {
        try {
            Recommendation recommendation = objectMapper.readValue(message, Recommendation.class);
            updateLatestRecommendations(recommendation);
            log.info("Received recommendation: {}", recommendation);
        } catch (JsonProcessingException e) {
            log.error("Error deserializing recommendation", e);
        }
    }
    
    private void updateLatestRecommendations(Recommendation recommendation) {
        // 更新最新推荐结果列表，保持最多10条
        latestRecommendations.add(0, recommendation);
        if (latestRecommendations.size() > 10) {
            latestRecommendations.remove(latestRecommendations.size() - 1);
        }
    }
    
    public List<Recommendation> getLatestRecommendations() {
        return new ArrayList<>(latestRecommendations);
    }
}
```

消费者配置：

```java
@Configuration
public class KafkaConsumerConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "recommendation-consumer");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return new DefaultKafkaConsumerFactory<>(props);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = 
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        return factory;
    }
}
```

[此处插入截图：Kafka消费者接收消息的日志]

### 1.5 Kafka集群监控与管理

为了方便监控和管理Kafka集群，我使用了Kafka Manager工具，配置如下：

```yaml
  kafka-manager:
    image: hlebalbau/kafka-manager:stable
    container_name: kafka-manager
    ports:
      - "9000:9000"
    environment:
      ZK_HOSTS: "zookeeper:2181"
      APPLICATION_SECRET: "random-secret"
    depends_on:
      - zookeeper
    networks:
      - kafka-net
```

通过Kafka Manager，我可以监控以下指标：

- Broker状态和配置
- Topic分区分布情况
- 消费者组消费进度
- 消息吞吐量统计

[此处插入截图：Kafka Manager界面展示集群状态]

## 2. 分布式Flink环境配置

### 2.1 Flink集群搭建

我搭建了一个由1个JobManager和2个TaskManager组成的Flink集群，用于实时数据处理。Flink集群配置如下：

```yaml
  jobmanager:
    image: flink:1.14.4
    container_name: jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
    networks:
      - kafka-net

  taskmanager1:
    image: flink:1.14.4
    container_name: taskmanager1
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
      - TASK_MANAGER_NUMBER_OF_TASK_SLOTS=2
    networks:
      - kafka-net

  taskmanager2:
    image: flink:1.14.4
    container_name: taskmanager2
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=jobmanager
      - TASK_MANAGER_NUMBER_OF_TASK_SLOTS=2
    networks:
      - kafka-net
```

[此处插入截图：Flink集群Web UI显示集群状态]

### 2.2 Flink与Kafka连接器配置

为了使Flink能够从Kafka消费数据并将处理结果发布回Kafka，我配置了Flink-Kafka连接器。在Maven项目中添加以下依赖：

```xml
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-connector-kafka_2.12</artifactId>
    <version>1.14.4</version>
</dependency>
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-streaming-java_2.12</artifactId>
    <version>1.14.4</version>
</dependency>
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-clients_2.12</artifactId>
    <version>1.14.4</version>
</dependency>
```

### 2.3 Flink数据源（Source）配置

从Kafka读取用户行为数据的Flink Source配置：

```java
public class RecommendationJob {

    public static void main(String[] args) throws Exception {
        // 设置Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 配置Kafka消费者属性
        Properties kafkaProps = new Properties();
        kafkaProps.setProperty("bootstrap.servers", "kafka1:9092,kafka2:9093,kafka3:9094");
        kafkaProps.setProperty("group.id", "flink-recommendation-consumer");
        
        // 创建Kafka数据源
        FlinkKafkaConsumer<String> userBehaviorSource = new FlinkKafkaConsumer<>(
            "user-behaviors",
            new SimpleStringSchema(),
            kafkaProps
        );
        
        // 设置消费起始位置
        userBehaviorSource.setStartFromLatest();
        
        // 添加数据源到Flink流
        DataStream<String> userBehaviorStream = env.addSource(userBehaviorSource);
        
        // 后续数据处理...
    }
}
```

[此处插入截图：Flink从Kafka读取数据的任务图]

### 2.4 Flink数据处理实现

在Flink中实现了用户行为数据的实时处理逻辑：

```java
// 解析JSON数据
DataStream<UserBehavior> parsedStream = userBehaviorStream
    .map(json -> {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, UserBehavior.class);
    })
    .returns(TypeInformation.of(UserBehavior.class));

// 按用户ID分组
KeyedStream<UserBehavior, Integer> keyedStream = parsedStream
    .keyBy(UserBehavior::getUserId);

// 设置时间窗口（5分钟滚动窗口）
WindowedStream<UserBehavior, Integer, TimeWindow> windowedStream = keyedStream
    .window(TumblingProcessingTimeWindows.of(Time.minutes(5)));

// 聚合用户行为并生成推荐
DataStream<Recommendation> recommendationStream = windowedStream
    .process(new RecommendationProcessFunction());
```

其中，`RecommendationProcessFunction`实现了推荐算法的核心逻辑：

```java
public class RecommendationProcessFunction 
        extends ProcessWindowFunction<UserBehavior, Recommendation, Integer, TimeWindow> {
    
    @Override
    public void process(
            Integer userId,
            Context context,
            Iterable<UserBehavior> behaviors,
            Collector<Recommendation> out) {
        
        // 收集用户行为
        List<UserBehavior> behaviorList = new ArrayList<>();
        behaviors.forEach(behaviorList::add);
        
        // 生成推荐（具体算法实现见下一节）
        List<Product> recommendedProducts = generateRecommendations(userId, behaviorList);
        
        // 输出推荐结果
        Recommendation recommendation = new Recommendation(
            userId,
            recommendedProducts,
            context.window().getEnd()
        );
        
        out.collect(recommendation);
    }
    
    // 推荐算法实现...
}
```

### 2.5 Flink数据输出（Sink）配置

将处理结果发送回Kafka的Flink Sink配置：

```java
// 序列化推荐结果为JSON
DataStream<String> serializedRecommendations = recommendationStream
    .map(recommendation -> {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(recommendation);
    });

// 配置Kafka生产者
FlinkKafkaProducer<String> kafkaSink = new FlinkKafkaProducer<>(
    "recommendations",           // 目标主题
    new SimpleStringSchema(),    // 序列化模式
    kafkaProps                   // Kafka配置属性
);

// 添加Sink
serializedRecommendations.addSink(kafkaSink);

// 执行Flink作业
env.execute("E-commerce Recommendation Job");
```

[此处插入截图：Flink作业执行状态]

## 3. 实现推荐系统工程

### 3.1 推荐算法设计

在本实验中，我实现了一个基于用户行为的协同过滤推荐算法。该算法主要包括以下步骤：

1. 收集用户行为数据（浏览、收藏、加购、购买）
2. 对不同行为类型赋予不同权重
3. 计算物品相似度
4. 基于用户历史行为和物品相似度生成推荐

算法核心实现：

```java
private List<Product> generateRecommendations(Integer userId, List<UserBehavior> behaviors) {
    // 1. 为不同行为类型分配权重
    Map<BehaviorType, Double> weights = new HashMap<>();
    weights.put(BehaviorType.VIEW, 1.0);
    weights.put(BehaviorType.FAVORITE, 2.0);
    weights.put(BehaviorType.CART, 3.0);
    weights.put(BehaviorType.PURCHASE, 4.0);
    
    // 2. 计算用户对每个商品的兴趣分数
    Map<Integer, Double> productScores = new HashMap<>();
    for (UserBehavior behavior : behaviors) {
        int productId = behavior.getProductId();
        double weight = weights.getOrDefault(behavior.getBehaviorType(), 1.0);
        productScores.put(productId, 
            productScores.getOrDefault(productId, 0.0) + weight);
    }
    
    // 3. 获取用户交互过的商品列表
    Set<Integer> interactedProducts = productScores.keySet();
    
    // 4. 获取相似商品并计算推荐分数
    Map<Integer, Double> recommendationScores = new HashMap<>();
    for (Integer productId : interactedProducts) {
        double userInterestScore = productScores.get(productId);
        
        // 获取相似商品（这里使用预先计算的相似度矩阵）
        Map<Integer, Double> similarProducts = getSimilarProducts(productId);
        
        // 计算推荐分数
        for (Map.Entry<Integer, Double> entry : similarProducts.entrySet()) {
            int similarProductId = entry.getKey();
            double similarity = entry.getValue();
            
            // 跳过用户已交互的商品
            if (interactedProducts.contains(similarProductId)) {
                continue;
            }
            
            // 累加推荐分数
            recommendationScores.put(similarProductId,
                recommendationScores.getOrDefault(similarProductId, 0.0) 
                + userInterestScore * similarity);
        }
    }
    
    // 5. 排序并返回前N个推荐商品
    return recommendationScores.entrySet().stream()
        .sorted(Map.Entry.<Integer, Double>comparingByValue().reversed())
        .limit(10)
        .map(entry -> productService.getProductById(entry.getKey()))
        .collect(Collectors.toList());
}

// 获取商品相似度（简化实现，实际应基于用户行为数据计算）
private Map<Integer, Double> getSimilarProducts(int productId) {
    // 在实际系统中，这应该是基于用户行为数据预先计算的相似度矩阵
    // 这里使用模拟数据
    Map<Integer, Double> similarProducts = new HashMap<>();
    
    // 获取商品类别
    Product product = productService.getProductById(productId);
    String category = product.getCategory();
    
    // 找出同类别的其他商品
    List<Product> sameCategory = productService.getProductsByCategory(category);
    
    // 计算简单相似度（基于价格和类别）
    for (Product other : sameCategory) {
        if (other.getId() != productId) {
            // 价格相似度（价格越接近，相似度越高）
            double priceDiff = Math.abs(product.getPrice() - other.getPrice());
            double maxPrice = Math.max(product.getPrice(), other.getPrice());
            double similarity = 1.0 - (priceDiff / maxPrice);
            
            similarProducts.put(other.getId(), similarity);
        }
    }
    
    return similarProducts;
}
```

[此处插入截图：推荐算法生成的推荐结果示例]

### 3.2 Flink状态管理

为了提高推荐系统的性能和准确性，我使用了Flink的状态管理功能来存储和更新用户行为模型：

```java
public class UserBehaviorModelFunction extends KeyedProcessFunction<Integer, UserBehavior, UserModel> {
    
    // 声明状态
    private ValueState<UserModel> userModelState;
    
    @Override
    public void open(Configuration parameters) {
        // 初始化状态
        ValueStateDescriptor<UserModel> descriptor = 
            new ValueStateDescriptor<>("user-model", TypeInformation.of(UserModel.class));
        userModelState = getRuntimeContext().getState(descriptor);
    }
    
    @Override
    public void processElement(
            UserBehavior behavior,
            Context ctx,
            Collector<UserModel> out) throws Exception {
        
        // 获取当前用户模型
        UserModel model = userModelState.value();
        if (model == null) {
            model = new UserModel(behavior.getUserId());
        }
        
        // 更新用户模型
        model.updateWithBehavior(behavior);
        
        // 保存更新后的模型
        userModelState.update(model);
        
        // 输出更新后的用户模型
        out.collect(model);
    }
}
```

### 3.3 实时特征提取

在推荐系统中，我实现了实时特征提取功能，从用户行为中提取关键特征：

```java
public class FeatureExtractor {
    
    public static Map<String, Double> extractFeatures(List<UserBehavior> behaviors) {
        Map<String, Double> features = new HashMap<>();
        
        // 提取类别偏好特征
        Map<String, Integer> categoryCount = new HashMap<>();
        for (UserBehavior behavior : behaviors) {
            String category = behavior.getProduct().getCategory();
            categoryCount.put(category, categoryCount.getOrDefault(category, 0) + 1);
        }
        
        // 归一化类别偏好
        int totalBehaviors = behaviors.size();
        for (Map.Entry<String, Integer> entry : categoryCount.entrySet()) {
            features.put("category_" + entry.getKey(), 
                         (double) entry.getValue() / totalBehaviors);
        }
        
        // 提取价格敏感度特征
        double avgPrice = behaviors.stream()
            .mapToDouble(b -> b.getProduct().getPrice())
            .average()
            .orElse(0.0);
        features.put("avg_price", avgPrice);
        
        // 提取行为类型分布特征
        Map<BehaviorType, Long> behaviorTypeCounts = behaviors.stream()
            .collect(Collectors.groupingBy(UserBehavior::getBehaviorType, Collectors.counting()));
        
        for (BehaviorType type : BehaviorType.values()) {
            long count = behaviorTypeCounts.getOrDefault(type, 0L);
            features.put("behavior_" + type.name(), (double) count / totalBehaviors);
        }
        
        return features;
    }
}
```

[此处插入截图：实时特征提取结果]

### 3.4 推荐结果评估

为了评估推荐系统的效果，我实现了以下评估指标的计算：

```java
public class RecommendationEvaluator {
    
    // 计算准确率（Precision）
    public double calculatePrecision(List<Integer> recommendedItems, List<Integer> actualItems) {
        if (recommendedItems.isEmpty()) {
            return 0.0;
        }
        
        int hits = 0;
        for (Integer item : recommendedItems) {
            if (actualItems.contains(item)) {
                hits++;
            }
        }
        
        return (double) hits / recommendedItems.size();
    }
    
    // 计算召回率（Recall）
    public double calculateRecall(List<Integer> recommendedItems, List<Integer> actualItems) {
        if (actualItems.isEmpty()) {
            return 0.0;
        }
        
        int hits = 0;
        for (Integer item : recommendedItems) {
            if (actualItems.contains(item)) {
                hits++;
            }
        }
        
        return (double) hits / actualItems.size();
    }
    
    // 计算F1分数
    public double calculateF1Score(double precision, double recall) {
        if (precision + recall == 0) {
            return 0.0;
        }
        
        return 2 * precision * recall / (precision + recall);
    }
}
```

## 4. 实现消息源软件工程

### 4.1 系统架构设计

消息源软件采用Spring Boot框架开发，主要包括以下组件：

1. Web界面：用于模拟用户操作和展示推荐结果
2. 用户行为生成器：模拟真实用户的浏览、收藏、加购、购买行为
3. Kafka生产者：将用户行为数据发送到Kafka
4. Kafka消费者：接收推荐系统生成的推荐结果
5. 数据服务：提供商品和用户数据

系统架构图：

[此处插入截图：系统架构图]

### 4.2 用户行为模拟实现

为了模拟真实的电商场景，我实现了用户行为生成器：

```java
@Service
public class DataGenerator {
    
    private final KafkaProducerService kafkaProducer;
    private final DataService dataService;
    private final Random random = new Random();
    
    public DataGenerator(KafkaProducerService kafkaProducer, DataService dataService) {
        this.kafkaProducer = kafkaProducer;
        this.dataService = dataService;
    }
    
    public void generateRandomUserBehaviors(int count) {
        List<User> users = dataService.getAllUsers();
        List<Product> products = dataService.getAllProducts();
        
        for (int i = 0; i < count; i++) {
            // 随机选择用户
            User user = users.get(random.nextInt(users.size()));
            
            // 随机选择商品
            Product product = products.get(random.nextInt(products.size()));
            
            // 随机选择行为类型
            BehaviorType behaviorType = getRandomBehaviorType();
            
            // 创建用户行为
            UserBehavior behavior = new UserBehavior();
            behavior.setUserId(user.getId());
            behavior.setProductId(product.getId());
            behavior.setBehaviorType(behaviorType);
            behavior.setTimestamp(System.currentTimeMillis());
            
            // 发送到Kafka
            kafkaProducer.sendUserBehavior(behavior);
        }
    }
    
    private BehaviorType getRandomBehaviorType() {
        // 设置不同行为类型的概率分布
        // 浏览:收藏:加购:购买 = 70:10:15:5
        int rand = random.nextInt(100);
        if (rand < 70) {
            return BehaviorType.VIEW;
        } else if (rand < 80) {
            return BehaviorType.FAVORITE;
        } else if (rand < 95) {
            return BehaviorType.CART;
        } else {
            return BehaviorType.PURCHASE;
        }
    }
}
```

### 4.3 Web界面实现

消息源软件提供了Web界面，用于模拟用户操作和展示推荐结果：

```java
@Controller
public class WebController {
    
    private final DataService dataService;
    private final KafkaProducerService producerService;
    private final KafkaConsumerService consumerService;
    private final DataGenerator dataGenerator;
    
    @GetMapping("/")
    public String index(Model model) {
        model.addAttribute("users", dataService.getAllUsers());
        model.addAttribute("products", dataService.getAllProducts());
        return "index";
    }
    
    @GetMapping("/recommendations")
    public String recommendations(Model model) {
        model.addAttribute("recommendations", consumerService.getLatestRecommendations());
        return "recommendations";
    }
    
    @PostMapping("/behavior")
    public String recordBehavior(
            @RequestParam("userId") int userId,
            @RequestParam("productId") int productId,
            @RequestParam("behaviorType") String behaviorType) {
        
        UserBehavior behavior = new UserBehavior();
        behavior.setUserId(userId);
        behavior.setProductId(productId);
        behavior.setBehaviorType(BehaviorType.valueOf(behaviorType));
        behavior.setTimestamp(System.currentTimeMillis());
        
        producerService.sendUserBehavior(behavior);
        
        return "redirect:/";
    }
    
    @PostMapping("/generate")
    public String generateData(@RequestParam("count") int count) {
        dataGenerator.generateRandomUserBehaviors(count);
        return "redirect:/";
    }
}
```

HTML模板（index.html）：

```html
<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>电商推荐系统 - 用户行为模拟</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>电商推荐系统 - 用户行为模拟</h1>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>记录用户行为</h5>
                    </div>
                    <div class="card-body">
                        <form action="/behavior" method="post">
                            <div class="form-group">
                                <label for="userId">用户</label>
                                <select class="form-control" id="userId" name="userId" required>
                                    <option th:each="user : ${users}" 
                                            th:value="${user.id}" 
                                            th:text="${user.name}"></option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="productId">商品</label>
                                <select class="form-control" id="productId" name="productId" required>
                                    <option th:each="product : ${products}" 
                                            th:value="${product.id}" 
                                            th:text="${product.name + ' (' + product.category + ', ¥' + product.price + ')'}"></option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="behaviorType">行为类型</label>
                                <select class="form-control" id="behaviorType" name="behaviorType" required>
                                    <option value="VIEW">浏览</option>
                                    <option value="FAVORITE">收藏</option>
                                    <option value="CART">加入购物车</option>
                                    <option value="PURCHASE">购买</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">提交</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>生成随机用户行为</h5>
                    </div>
                    <div class="card-body">
                        <form action="/generate" method="post">
                            <div class="form-group">
                                <label for="count">生成数量</label>
                                <input type="number" class="form-control" id="count" name="count" value="10" min="1" max="100" required>
                            </div>
                            <button type="submit" class="btn btn-success">生成</button>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>查看推荐结果</h5>
                    </div>
                    <div class="card-body">
                        <a href="/recommendations" class="btn btn-info">查看推荐</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
```

[此处插入截图：Web界面首页]

### 4.4 数据模型设计

消息源软件中的主要数据模型包括：

1. 用户（User）：

```java
public class User {
    private int id;
    private String name;
    private String gender;
    private int age;
    
    // 构造函数、getter和setter方法
}
```

2. 商品（Product）：

```java
public class Product {
    private int id;
    private String name;
    private String category;
    private double price;
    private String description;
    
    // 构造函数、getter和setter方法
}
```

3. 用户行为（UserBehavior）：

```java
public class UserBehavior {
    private int userId;
    private int productId;
    private BehaviorType behaviorType;
    private long timestamp;
    
    // 构造函数、getter和setter方法
}

public enum BehaviorType {
    VIEW,      // 浏览
    FAVORITE,  // 收藏
    CART,      // 加入购物车
    PURCHASE   // 购买
}
```

4. 推荐结果（Recommendation）：

```java
public class Recommendation {
    private int userId;
    private List<Product> products;
    private long timestamp;
    
    // 构造函数、getter和setter方法
}
```

### 4.5 系统集成与部署

整个系统的部署使用Docker Compose实现，包括Kafka集群、Flink集群和消息源软件：

```yaml
version: '3'
services:
  # Kafka和Zookeeper配置（见前文）
  
  # Flink配置（见前文）
  
  # 消息源软件
  message-source:
    build: ./message-source
    container_name: message-source
    ports:
      - "8080:8080"
    environment:
      - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka1:9092,kafka2:9093,kafka3:9094
    depends_on:
      - kafka1
      - kafka2
      - kafka3
    networks:
      - kafka-net
  
  # 推荐系统
  recommendation-system:
    build: ./recommendation-system
    container_name: recommendation-system
    depends_on:
      - jobmanager
      - kafka1
    networks:
      - kafka-net

networks:
  kafka-net:
    driver: bridge
```

启动脚本（start_system.sh）：

```bash
#!/bin/bash

echo "Starting distributed e-commerce recommendation system..."

# 启动Docker容器
docker-compose up -d

# 等待服务启动
echo "Waiting for services to start..."
sleep 10

# 创建Kafka主题（如果不存在）
docker exec kafka1 kafka-topics.sh --create --if-not-exists --topic user-behaviors --partitions 3 --replication-factor 2 --bootstrap-server kafka1:9092
docker exec kafka1 kafka-topics.sh --create --if-not-exists --topic recommendations --partitions 3 --replication-factor 2 --bootstrap-server kafka1:9092

echo "System started successfully!"
echo "Web interface: http://localhost:8080"
echo "Flink dashboard: http://localhost:8081"
echo "Kafka Manager: http://localhost:9000"
```

[此处插入截图：系统启动后的Docker容器状态]

## 5. 总结与反思

### 5.1 实验成果

在本实验中，我成功实现了一个完整的分布式实时电商推荐系统，包括：

1. 搭建了高可用的分布式Kafka集群，实现了消息的发布、订阅和Topic管理
2. 配置了分布式Flink环境，实现了实时数据处理和推荐算法
3. 开发了基于协同过滤的推荐算法，能够根据用户行为生成个性化推荐
4. 实现了消息源软件，模拟用户行为并展示推荐结果

系统具有以下特点：

- 高可用性：通过Kafka的分区和复制机制确保数据不丢失
- 实时性：使用Flink流处理框架实现毫秒级的数据处理和推荐
- 可扩展性：分布式架构设计使系统可以水平扩展
- 容错性：系统能够处理节点故障和网络分区等异常情况

### 5.2 技术挑战与解决方案

在实验过程中，我遇到了以下技术挑战：

1. **Kafka集群配置复杂**
   - 挑战：多节点Kafka集群的网络配置和监听地址设置
   - 解决方案：使用Docker网络和环境变量配置，确保各节点间通信正常

2. **Flink与Kafka集成问题**
   - 挑战：Flink消费Kafka数据时的序列化和反序列化
   - 解决方案：使用通用的JSON序列化方案，确保数据格式一致性

3. **推荐算法性能优化**
   - 挑战：实时推荐算法的计算复杂度高
   - 解决方案：使用Flink的状态管理功能缓存中间结果，减少重复计算

4. **系统集成与测试**
   - 挑战：分布式系统各组件的协调和测试
   - 解决方案：使用Docker Compose统一管理所有服务，编写自动化测试脚本

### 5.3 改进方向

未来可以从以下几个方面对系统进行改进：

1. **推荐算法优化**：
   - 引入深度学习模型，如神经协同过滤（NCF）
   - 增加上下文感知推荐，考虑时间、位置等因素

2. **系统扩展**：
   - 增加更多数据源，如用户画像、商品特征
   - 实现A/B测试框架，评估不同推荐策略的效果

3. **监控与运维**：
   - 增加系统监控和告警功能
   - 实现自动扩缩容和故障恢复机制

4. **用户体验**：
   - 优化Web界面，提供更丰富的交互功能
   - 增加推荐解释功能，提高推荐透明度

### 5.4 学习心得

通过本次实验，我深入理解了分布式系统的设计原则和实现方法，特别是在以下方面获得了宝贵经验：

1. 分布式消息队列的配置和使用
2. 流处理框架的应用场景和编程模型
3. 推荐系统的核心算法和评估方法
4. 分布式系统的部署和运维

这些知识和技能对我未来从事分布式系统和大数据处理相关工作具有重要价值。

package com.ecommerce.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

/**
 * JSON序列化Schema
 */
public class JsonSerializationSchema<T> implements KafkaRecordSerializationSchema<T> {
    
    private final String topic;
    private final ObjectMapper objectMapper;
    
    public JsonSerializationSchema(String topic) {
        this.topic = topic;
        this.objectMapper = new ObjectMapper();
    }
    
    @Nullable
    @Override
    public ProducerRecord<byte[], byte[]> serialize(T element, KafkaSinkContext context, Long timestamp) {
        try {
            byte[] value = objectMapper.writeValueAsBytes(element);
            return new ProducerRecord<>(topic, value);
        } catch (Exception e) {
            System.err.println("JSON序列化失败: " + element);
            e.printStackTrace();
            return null;
        }
    }
}

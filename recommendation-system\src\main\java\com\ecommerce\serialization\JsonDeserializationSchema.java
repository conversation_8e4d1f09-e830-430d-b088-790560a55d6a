package com.ecommerce.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;

/**
 * JSON反序列化Schema
 */
public class JsonDeserializationSchema<T> implements DeserializationSchema<T> {
    
    private final Class<T> clazz;
    private final ObjectMapper objectMapper;
    
    public JsonDeserializationSchema(Class<T> clazz) {
        this.clazz = clazz;
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public T deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            return objectMapper.readValue(message, clazz);
        } catch (Exception e) {
            System.err.println("JSON反序列化失败: " + new String(message));
            e.printStackTrace();
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(T nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<T> getProducedType() {
        return TypeInformation.of(clazz);
    }
}

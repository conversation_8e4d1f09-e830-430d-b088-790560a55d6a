# WSL环境使用指南

## 🎯 概述

本指南将帮助您在WSL (Windows Subsystem for Linux) 环境中运行分布式实时电商推荐系统。

## 📋 前置条件

### 1. 确认WSL状态
```bash
# 在Windows PowerShell中执行
wsl --status
wsl --list --verbose
```

### 2. 进入WSL环境
```bash
# 方法1: 直接进入WSL
wsl

# 方法2: 进入项目目录
wsl bash -c "cd /mnt/c/Users/<USER>/Desktop/first"
```

## 🚀 快速开始

### 步骤1: 进入WSL并导航到项目目录
```bash
# 在Windows PowerShell中执行
wsl
cd /mnt/c/Users/<USER>/Desktop/first
```

### 步骤2: 检查环境
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查项目文件
ls -la
```

### 步骤3: 给脚本执行权限
```bash
chmod +x *.sh
```

## 🔧 系统部署

### 方案1: 完整部署（推荐用于演示）
```bash
# 一键部署整个系统
./build-and-run.sh deploy
```

### 方案2: 分步部署（推荐用于学习）

#### 2.1 配置Kafka环境
```bash
# 配置Kafka集群
./kafka-setup.sh

# 启动Kafka集群
./kafka-manager.sh start

# 检查Kafka状态
./kafka-manager.sh topics
```

#### 2.2 配置Flink环境
```bash
# 配置Flink集群
./flink-setup.sh

# 启动Flink集群
./flink-manager.sh start

# 检查Flink状态
./flink-manager.sh status
```

#### 2.3 构建和部署应用
```bash
# 构建推荐系统
cd recommendation-system
mvn clean package -DskipTests
cd ..

# 构建消息源软件
cd message-source
mvn clean package -DskipTests
cd ..

# 提交Flink作业
./flink-manager.sh submit recommendation-system/target/recommendation-system-1.0-SNAPSHOT.jar
```

#### 2.4 启动消息源软件
```bash
# 启动消息源软件
java -jar message-source/target/message-source-1.0-SNAPSHOT.jar
```

## 🖥️ 系统监控

### 1. Flink Web UI
```bash
# 在WSL中启动后，在Windows浏览器中访问
# http://localhost:8081
```

### 2. 系统状态检查
```bash
# 检查整体状态
./build-and-run.sh status

# 检查Kafka状态
./kafka-manager.sh describe

# 检查Flink作业
./flink-manager.sh list
```

### 3. 日志查看
```bash
# 查看Flink日志
./flink-manager.sh logs

# 查看Kafka日志
ls kafka-cluster/logs/

# 实时查看日志
tail -f kafka-cluster/logs/*.log
```

## 🧪 测试和验证

### 1. 基础功能测试
启动消息源软件后，按照以下步骤测试：

```
1. 选择 "1. 创建新商品"
   - 商品ID: electronics001
   - 商品名称: 智能手机
   - 商品分类: electronics
   - 商品价格: 999.99
   - 商品描述: 最新款智能手机

2. 选择 "2. 模拟用户行为"
   - 用户ID: user001
   - 商品ID: electronics001
   - 行为类型: view
   - 商品分类: electronics

3. 重复步骤2，使用不同的行为类型：click, add_to_cart, purchase

4. 观察推荐结果的生成
```

### 2. 自动化测试
```bash
# 在消息源软件中选择 "3. 启动自动模拟"
# 系统将自动生成用户行为和推荐结果
```

## 🛠️ 常见问题解决

### 1. 端口占用问题
```bash
# 检查端口占用
netstat -tulpn | grep :9092  # Kafka
netstat -tulpn | grep :8081  # Flink

# 杀死占用进程
sudo kill -9 <PID>
```

### 2. Java内存不足
```bash
# 设置Java内存参数
export JAVA_OPTS="-Xmx2g -Xms1g"

# 或者修改脚本中的内存设置
```

### 3. Maven依赖下载慢
```bash
# 使用阿里云镜像
mkdir -p ~/.m2
cat > ~/.m2/settings.xml << EOF
<settings>
  <mirrors>
    <mirror>
      <id>aliyun</id>
      <mirrorOf>central</mirrorOf>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
  </mirrors>
</settings>
EOF
```

### 4. 权限问题
```bash
# 给予执行权限
chmod +x *.sh

# 如果需要sudo权限
sudo ./script-name.sh
```

## 📊 性能优化

### 1. WSL性能优化
```bash
# 在Windows中创建 .wslconfig 文件
# 位置: C:\Users\<USER>\.wslconfig
[wsl2]
memory=8GB
processors=4
swap=2GB
```

### 2. Java性能优化
```bash
# 设置JVM参数
export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC"
```

## 🔄 系统重启

### 完全重启系统
```bash
# 停止所有服务
./build-and-run.sh stop

# 等待几秒钟
sleep 5

# 重新启动
./build-and-run.sh start
```

### 重启单个组件
```bash
# 重启Kafka
./kafka-manager.sh stop
./kafka-manager.sh start

# 重启Flink
./flink-manager.sh stop
./flink-manager.sh start
```

## 📝 实验报告建议

### 1. 截图收集
- Flink Web UI界面
- 消息源软件运行界面
- 推荐结果展示
- 系统监控数据

### 2. 性能数据
```bash
# 收集性能数据
./build-and-run.sh status > performance.log
./kafka-manager.sh describe >> performance.log
./flink-manager.sh list >> performance.log
```

### 3. 日志分析
```bash
# 导出关键日志
cp kafka-cluster/logs/*.log ./logs/
cp flink-cluster/logs/*.log ./logs/
```

## 🎯 演示准备

### 1. 演示脚本
```bash
# 运行演示脚本
./demo-run.sh
```

### 2. 实时演示流程
1. 启动系统：`./build-and-run.sh deploy`
2. 打开Flink Web UI：http://localhost:8081
3. 启动消息源软件
4. 演示用户行为模拟
5. 展示推荐结果生成
6. 展示系统监控数据

## 🔚 系统清理

### 完全清理
```bash
# 停止所有服务
./build-and-run.sh stop

# 清理临时文件
rm -rf kafka-cluster/logs/*
rm -rf flink-cluster/logs/*
rm -rf target/
```

## 💡 小贴士

1. **保持WSL运行**：确保WSL在使用过程中保持运行状态
2. **网络配置**：WSL使用NAT模式，localhost访问正常
3. **文件权限**：Windows和Linux文件权限可能不同，注意chmod
4. **资源监控**：使用`htop`或`top`监控系统资源使用
5. **备份重要数据**：定期备份配置文件和日志

通过以上指南，您可以在WSL环境中完整地运行和测试分布式实时电商推荐系统！

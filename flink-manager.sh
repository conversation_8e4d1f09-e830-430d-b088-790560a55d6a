#!/bin/bash
# Flink集群管理脚本

FLINK_HOME="./flink-1.17.2"
CONFIG_DIR="./flink-cluster/config"

function start_flink() {
    echo "启动Flink集群..."
    
    # 复制配置文件到Flink目录
    cp $CONFIG_DIR/flink-conf.yaml $FLINK_HOME/conf/
    cp $CONFIG_DIR/workers $FLINK_HOME/conf/
    cp $CONFIG_DIR/masters $FLINK_HOME/conf/
    
    # 启动JobManager
    echo "启动JobManager..."
    $FLINK_HOME/bin/jobmanager.sh start
    sleep 5
    
    # 启动TaskManagers
    echo "启动TaskManagers..."
    $FLINK_HOME/bin/taskmanager.sh start
    sleep 3
    
    echo "Flink集群已启动"
    echo "Web UI地址: http://localhost:8081"
}

function stop_flink() {
    echo "停止Flink集群..."
    $FLINK_HOME/bin/stop-cluster.sh
    echo "Flink集群已停止"
}

function status_flink() {
    echo "检查Flink集群状态..."
    
    # 检查JobManager进程
    if pgrep -f "org.apache.flink.runtime.entrypoint" > /dev/null; then
        echo "✓ JobManager正在运行"
    else
        echo "✗ JobManager未运行"
    fi
    
    # 检查TaskManager进程
    TASKMANAGER_COUNT=$(pgrep -f "org.apache.flink.runtime.taskexecutor" | wc -l)
    echo "✓ TaskManager数量: $TASKMANAGER_COUNT"
    
    # 检查Web UI
    if curl -s http://localhost:8081 > /dev/null; then
        echo "✓ Web UI可访问: http://localhost:8081"
    else
        echo "✗ Web UI不可访问"
    fi
}

function submit_job() {
    if [ -z "$2" ]; then
        echo "用法: $0 submit <jar文件路径>"
        exit 1
    fi
    
    echo "提交Flink作业: $2"
    $FLINK_HOME/bin/flink run $2
}

function list_jobs() {
    echo "当前运行的作业："
    $FLINK_HOME/bin/flink list
}

function cancel_job() {
    if [ -z "$2" ]; then
        echo "用法: $0 cancel <job-id>"
        exit 1
    fi
    
    echo "取消作业: $2"
    $FLINK_HOME/bin/flink cancel $2
}

function logs() {
    echo "Flink日志文件："
    ls -la $FLINK_HOME/log/
    echo ""
    echo "最新JobManager日志："
    tail -20 $FLINK_HOME/log/flink-*-jobmanager-*.log
}

case "$1" in
    start)
        start_flink
        ;;
    stop)
        stop_flink
        ;;
    status)
        status_flink
        ;;
    submit)
        submit_job $@
        ;;
    list)
        list_jobs
        ;;
    cancel)
        cancel_job $@
        ;;
    logs)
        logs
        ;;
    *)
        echo "用法: $0 {start|stop|status|submit|list|cancel|logs}"
        echo "  start           - 启动Flink集群"
        echo "  stop            - 停止Flink集群"
        echo "  status          - 检查集群状态"
        echo "  submit <jar>    - 提交作业"
        echo "  list            - 列出运行中的作业"
        echo "  cancel <job-id> - 取消作业"
        echo "  logs            - 查看日志"
        exit 1
        ;;
esac

#!/bin/bash
# Kafka集群配置脚本

echo "=== 分布式Kafka环境配置 ==="

# 1. 创建必要的目录
mkdir -p kafka-cluster/{hadoop01,hadoop02,hadoop03}
mkdir -p kafka-cluster/config
mkdir -p kafka-cluster/logs

# 2. 下载Kafka（如果没有的话）
KAFKA_VERSION="2.13-3.6.0"
KAFKA_DIR="kafka_${KAFKA_VERSION}"

if [ ! -d "$KAFKA_DIR" ]; then
    echo "下载Kafka..."
    wget https://downloads.apache.org/kafka/3.6.0/kafka_${KAFKA_VERSION}.tgz
    tar -xzf kafka_${KAFKA_VERSION}.tgz
fi

# 3. 生成Zookeeper配置
cat > kafka-cluster/config/zookeeper.properties << EOF
# Zookeeper配置
dataDir=./kafka-cluster/logs/zookeeper
clientPort=2181
maxClientCnxns=0
admin.enableServer=false
tickTime=2000
initLimit=10
syncLimit=5

# 集群配置
server.1=hadoop01:2888:3888
server.2=hadoop02:2888:3888
server.3=hadoop03:2888:3888
EOF

# 4. 生成Kafka Broker配置文件
# Hadoop01 配置
cat > kafka-cluster/config/server-hadoop01.properties << EOF
# Kafka Broker 1 配置 (Hadoop01)
broker.id=1
listeners=PLAINTEXT://hadoop01:9092
advertised.listeners=PLAINTEXT://hadoop01:9092
log.dirs=./kafka-cluster/logs/kafka-logs-1
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=hadoop01:2181,hadoop02:2181,hadoop03:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF

# Hadoop02 配置
cat > kafka-cluster/config/server-hadoop02.properties << EOF
# Kafka Broker 2 配置 (Hadoop02)
broker.id=2
listeners=PLAINTEXT://hadoop02:9092
advertised.listeners=PLAINTEXT://hadoop02:9092
log.dirs=./kafka-cluster/logs/kafka-logs-2
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=hadoop01:2181,hadoop02:2181,hadoop03:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF

# Hadoop03 配置
cat > kafka-cluster/config/server-hadoop03.properties << EOF
# Kafka Broker 3 配置 (Hadoop03)
broker.id=3
listeners=PLAINTEXT://hadoop03:9092
advertised.listeners=PLAINTEXT://hadoop03:9092
log.dirs=./kafka-cluster/logs/kafka-logs-3
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=hadoop01:2181,hadoop02:2181,hadoop03:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF

echo "Kafka配置文件已生成完成！"
echo "配置文件位置："
echo "- Zookeeper: kafka-cluster/config/zookeeper.properties"
echo "- Hadoop01: kafka-cluster/config/server-hadoop01.properties"
echo "- Hadoop02: kafka-cluster/config/server-hadoop02.properties"
echo "- Hadoop03: kafka-cluster/config/server-hadoop03.properties"

package com.ecommerce.source;

import com.ecommerce.model.Product;
import com.ecommerce.model.UserBehavior;
import com.ecommerce.model.Recommendation;
import com.ecommerce.producer.ProductProducer;
import com.ecommerce.producer.UserBehaviorProducer;
import com.ecommerce.consumer.RecommendationConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Scanner;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 消息源软件主应用程序
 * 模拟电商用户行为，生产Kafka消息，并接收推荐结果
 */
public class MessageSourceApp {
    
    private static final String KAFKA_BROKERS = "localhost:9092";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private ProductProducer productProducer;
    private UserBehaviorProducer behaviorProducer;
    private RecommendationConsumer recommendationConsumer;
    private ExecutorService executorService;
    
    public MessageSourceApp() {
        this.productProducer = new ProductProducer(KAFKA_BROKERS);
        this.behaviorProducer = new UserBehaviorProducer(KAFKA_BROKERS);
        this.recommendationConsumer = new RecommendationConsumer(KAFKA_BROKERS);
        this.executorService = Executors.newFixedThreadPool(3);
    }
    
    public void start() {
        System.out.println("=".repeat(60));
        System.out.println("    电商实时推荐系统 - 消息源软件");
        System.out.println("=".repeat(60));
        
        // 启动推荐结果消费者
        executorService.submit(() -> {
            recommendationConsumer.startConsuming(this::handleRecommendation);
        });
        
        // 主菜单循环
        Scanner scanner = new Scanner(System.in);
        boolean running = true;
        
        while (running) {
            showMenu();
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    createProduct(scanner);
                    break;
                case "2":
                    simulateUserBehavior(scanner);
                    break;
                case "3":
                    startAutoSimulation();
                    break;
                case "4":
                    showStatistics();
                    break;
                case "5":
                    running = false;
                    break;
                default:
                    System.out.println("无效选择，请重新输入！");
            }
        }
        
        shutdown();
        scanner.close();
    }
    
    private void showMenu() {
        System.out.println("\n" + "-".repeat(40));
        System.out.println("请选择操作:");
        System.out.println("1. 创建新商品");
        System.out.println("2. 模拟用户行为");
        System.out.println("3. 启动自动模拟");
        System.out.println("4. 查看统计信息");
        System.out.println("5. 退出系统");
        System.out.print("请输入选择 (1-5): ");
    }
    
    private void createProduct(Scanner scanner) {
        try {
            System.out.println("\n=== 创建新商品 ===");
            
            System.out.print("商品ID: ");
            String productId = scanner.nextLine().trim();
            
            System.out.print("商品名称: ");
            String productName = scanner.nextLine().trim();
            
            System.out.print("商品分类 (electronics/clothing/books/home): ");
            String category = scanner.nextLine().trim();
            
            System.out.print("商品价格: ");
            double price = Double.parseDouble(scanner.nextLine().trim());
            
            System.out.print("商品描述: ");
            String description = scanner.nextLine().trim();
            
            Product product = new Product(productId, productName, category, price, description);
            
            productProducer.sendProduct(product);
            
            System.out.println("✓ 商品创建成功并发送到Kafka!");
            System.out.println("商品信息: " + product);
            
        } catch (Exception e) {
            System.err.println("✗ 创建商品失败: " + e.getMessage());
        }
    }
    
    private void simulateUserBehavior(Scanner scanner) {
        try {
            System.out.println("\n=== 模拟用户行为 ===");
            
            System.out.print("用户ID: ");
            String userId = scanner.nextLine().trim();
            
            System.out.print("商品ID: ");
            String productId = scanner.nextLine().trim();
            
            System.out.print("行为类型 (view/click/add_to_cart/purchase): ");
            String actionType = scanner.nextLine().trim();
            
            System.out.print("商品分类: ");
            String category = scanner.nextLine().trim();
            
            UserBehavior behavior = new UserBehavior(userId, productId, actionType, category);
            
            behaviorProducer.sendUserBehavior(behavior);
            
            System.out.println("✓ 用户行为模拟成功并发送到Kafka!");
            System.out.println("行为信息: " + behavior);
            
        } catch (Exception e) {
            System.err.println("✗ 模拟用户行为失败: " + e.getMessage());
        }
    }
    
    private void startAutoSimulation() {
        System.out.println("\n=== 启动自动模拟 ===");
        System.out.println("自动模拟将在后台运行，每5秒生成一次用户行为...");
        System.out.println("按回车键停止自动模拟");
        
        executorService.submit(() -> {
            AutoSimulator simulator = new AutoSimulator(behaviorProducer, productProducer);
            simulator.startSimulation();
        });
        
        Scanner scanner = new Scanner(System.in);
        scanner.nextLine(); // 等待用户按回车
        
        System.out.println("自动模拟已停止");
    }
    
    private void showStatistics() {
        System.out.println("\n=== 系统统计信息 ===");
        System.out.println("商品生产消息数: " + productProducer.getMessageCount());
        System.out.println("用户行为消息数: " + behaviorProducer.getMessageCount());
        System.out.println("推荐结果接收数: " + recommendationConsumer.getMessageCount());
    }
    
    private void handleRecommendation(Recommendation recommendation) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("🎯 收到推荐结果!");
        System.out.println("用户ID: " + recommendation.getUserId());
        System.out.println("推荐商品: " + recommendation.getRecommendedProducts());
        System.out.println("算法: " + recommendation.getAlgorithm());
        System.out.println("置信度: " + recommendation.getConfidence());
        System.out.println("推荐理由: " + recommendation.getReason());
        System.out.println("时间: " + new java.util.Date(recommendation.getTimestamp()));
        System.out.println("=".repeat(50));
    }
    
    private void shutdown() {
        System.out.println("\n正在关闭系统...");
        
        if (productProducer != null) {
            productProducer.close();
        }
        
        if (behaviorProducer != null) {
            behaviorProducer.close();
        }
        
        if (recommendationConsumer != null) {
            recommendationConsumer.close();
        }
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        System.out.println("系统已关闭");
    }
    
    public static void main(String[] args) {
        MessageSourceApp app = new MessageSourceApp();
        app.start();
    }
}

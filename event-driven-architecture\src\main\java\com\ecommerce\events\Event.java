package com.ecommerce.events;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import java.time.Instant;
import java.util.UUID;

/**
 * 事件基类 - 所有业务事件的基础
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "eventType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = UserBehaviorOccurred.class, name = "UserBehaviorOccurred"),
    @JsonSubTypes.Type(value = ProductCreated.class, name = "ProductCreated"),
    @JsonSubTypes.Type(value = ProductUpdated.class, name = "ProductUpdated"),
    @JsonSubTypes.Type(value = RecommendationGenerated.class, name = "RecommendationGenerated"),
    @JsonSubTypes.Type(value = UserProfileUpdated.class, name = "UserProfileUpdated")
})
public abstract class Event {
    
    private final String eventId;
    private final String eventType;
    private final Instant timestamp;
    private final String aggregateId;
    private final Long version;
    
    protected Event(String aggregateId, Long version) {
        this.eventId = UUID.randomUUID().toString();
        this.eventType = this.getClass().getSimpleName();
        this.timestamp = Instant.now();
        this.aggregateId = aggregateId;
        this.version = version;
    }
    
    // Getters
    public String getEventId() { return eventId; }
    public String getEventType() { return eventType; }
    public Instant getTimestamp() { return timestamp; }
    public String getAggregateId() { return aggregateId; }
    public Long getVersion() { return version; }
    
    @Override
    public String toString() {
        return String.format("%s{eventId='%s', aggregateId='%s', timestamp=%s}", 
            eventType, eventId, aggregateId, timestamp);
    }
}

/**
 * 用户行为事件
 */
class UserBehaviorOccurred extends Event {
    private final String userId;
    private final String productId;
    private final String actionType;
    private final String sessionId;
    private final BehaviorContext context;
    
    public UserBehaviorOccurred(String userId, String productId, String actionType, 
                               String sessionId, BehaviorContext context, Long version) {
        super(userId, version);
        this.userId = userId;
        this.productId = productId;
        this.actionType = actionType;
        this.sessionId = sessionId;
        this.context = context;
    }
    
    // Getters
    public String getUserId() { return userId; }
    public String getProductId() { return productId; }
    public String getActionType() { return actionType; }
    public String getSessionId() { return sessionId; }
    public BehaviorContext getContext() { return context; }
    
    public static class BehaviorContext {
        private final String deviceType;
        private final String location;
        private final String referrer;
        private final String userAgent;
        
        public BehaviorContext(String deviceType, String location, String referrer, String userAgent) {
            this.deviceType = deviceType;
            this.location = location;
            this.referrer = referrer;
            this.userAgent = userAgent;
        }
        
        // Getters
        public String getDeviceType() { return deviceType; }
        public String getLocation() { return location; }
        public String getReferrer() { return referrer; }
        public String getUserAgent() { return userAgent; }
    }
}

/**
 * 商品创建事件
 */
class ProductCreated extends Event {
    private final String productId;
    private final String name;
    private final String category;
    private final Double price;
    private final ProductAttributes attributes;
    
    public ProductCreated(String productId, String name, String category, 
                         Double price, ProductAttributes attributes, Long version) {
        super(productId, version);
        this.productId = productId;
        this.name = name;
        this.category = category;
        this.price = price;
        this.attributes = attributes;
    }
    
    // Getters
    public String getProductId() { return productId; }
    public String getName() { return name; }
    public String getCategory() { return category; }
    public Double getPrice() { return price; }
    public ProductAttributes getAttributes() { return attributes; }
    
    public static class ProductAttributes {
        private final String brand;
        private final String model;
        private final String color;
        private final String description;
        
        public ProductAttributes(String brand, String model, String color, String description) {
            this.brand = brand;
            this.model = model;
            this.color = color;
            this.description = description;
        }
        
        // Getters
        public String getBrand() { return brand; }
        public String getModel() { return model; }
        public String getColor() { return color; }
        public String getDescription() { return description; }
    }
}

/**
 * 商品更新事件
 */
class ProductUpdated extends Event {
    private final String productId;
    private final String fieldName;
    private final Object oldValue;
    private final Object newValue;
    
    public ProductUpdated(String productId, String fieldName, Object oldValue, Object newValue, Long version) {
        super(productId, version);
        this.productId = productId;
        this.fieldName = fieldName;
        this.oldValue = oldValue;
        this.newValue = newValue;
    }
    
    // Getters
    public String getProductId() { return productId; }
    public String getFieldName() { return fieldName; }
    public Object getOldValue() { return oldValue; }
    public Object getNewValue() { return newValue; }
}

/**
 * 推荐生成事件
 */
class RecommendationGenerated extends Event {
    private final String userId;
    private final java.util.List<RecommendationItem> recommendations;
    private final String algorithm;
    private final String modelVersion;
    private final Double confidence;
    
    public RecommendationGenerated(String userId, java.util.List<RecommendationItem> recommendations,
                                 String algorithm, String modelVersion, Double confidence, Long version) {
        super("recommendation-" + userId, version);
        this.userId = userId;
        this.recommendations = recommendations;
        this.algorithm = algorithm;
        this.modelVersion = modelVersion;
        this.confidence = confidence;
    }
    
    // Getters
    public String getUserId() { return userId; }
    public java.util.List<RecommendationItem> getRecommendations() { return recommendations; }
    public String getAlgorithm() { return algorithm; }
    public String getModelVersion() { return modelVersion; }
    public Double getConfidence() { return confidence; }
    
    public static class RecommendationItem {
        private final String productId;
        private final Double score;
        private final String reason;
        
        public RecommendationItem(String productId, Double score, String reason) {
            this.productId = productId;
            this.score = score;
            this.reason = reason;
        }
        
        // Getters
        public String getProductId() { return productId; }
        public Double getScore() { return score; }
        public String getReason() { return reason; }
    }
}

/**
 * 用户画像更新事件
 */
class UserProfileUpdated extends Event {
    private final String userId;
    private final java.util.Map<String, Double> categoryPreferences;
    private final java.util.Set<String> purchasedProducts;
    private final UserDemographics demographics;
    
    public UserProfileUpdated(String userId, java.util.Map<String, Double> categoryPreferences,
                            java.util.Set<String> purchasedProducts, UserDemographics demographics, Long version) {
        super(userId, version);
        this.userId = userId;
        this.categoryPreferences = categoryPreferences;
        this.purchasedProducts = purchasedProducts;
        this.demographics = demographics;
    }
    
    // Getters
    public String getUserId() { return userId; }
    public java.util.Map<String, Double> getCategoryPreferences() { return categoryPreferences; }
    public java.util.Set<String> getPurchasedProducts() { return purchasedProducts; }
    public UserDemographics getDemographics() { return demographics; }
    
    public static class UserDemographics {
        private final String ageGroup;
        private final String gender;
        private final String location;
        private final String incomeLevel;
        
        public UserDemographics(String ageGroup, String gender, String location, String incomeLevel) {
            this.ageGroup = ageGroup;
            this.gender = gender;
            this.location = location;
            this.incomeLevel = incomeLevel;
        }
        
        // Getters
        public String getAgeGroup() { return ageGroup; }
        public String getGender() { return gender; }
        public String getLocation() { return location; }
        public String getIncomeLevel() { return incomeLevel; }
    }
}

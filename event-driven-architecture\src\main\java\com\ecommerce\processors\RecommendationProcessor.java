package com.ecommerce.processors;

import com.ecommerce.events.*;
import com.ecommerce.eventstore.EventStore;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件驱动推荐处理器
 * 基于事件流实时计算推荐结果
 */
public class RecommendationProcessor extends KeyedProcessFunction<String, Event, Event> {
    
    // 用户行为状态
    private transient MapState<String, UserBehaviorAggregate> userBehaviors;
    
    // 商品信息状态
    private transient MapState<String, ProductAggregate> products;
    
    // 用户画像状态
    private transient ValueState<UserProfileAggregate> userProfile;
    
    // 推荐模型状态
    private transient ValueState<RecommendationModel> model;
    
    // 推荐间隔控制
    private static final long RECOMMENDATION_INTERVAL = 30000; // 30秒
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        userBehaviors = getRuntimeContext().getMapState(
            new MapStateDescriptor<>("userBehaviors", String.class, UserBehaviorAggregate.class));
            
        products = getRuntimeContext().getMapState(
            new MapStateDescriptor<>("products", String.class, ProductAggregate.class));
            
        userProfile = getRuntimeContext().getState(
            new ValueStateDescriptor<>("userProfile", UserProfileAggregate.class));
            
        model = getRuntimeContext().getState(
            new ValueStateDescriptor<>("model", RecommendationModel.class));
    }
    
    @Override
    public void processElement(Event event, Context context, Collector<Event> collector) throws Exception {
        
        // 根据事件类型进行不同处理
        switch (event.getEventType()) {
            case "UserBehaviorOccurred":
                handleUserBehavior((UserBehaviorOccurred) event, context, collector);
                break;
                
            case "ProductCreated":
                handleProductCreated((ProductCreated) event);
                break;
                
            case "ProductUpdated":
                handleProductUpdated((ProductUpdated) event);
                break;
                
            default:
                // 忽略未知事件类型
                break;
        }
    }
    
    /**
     * 处理用户行为事件
     */
    private void handleUserBehavior(UserBehaviorOccurred event, Context context, Collector<Event> collector) throws Exception {
        String userId = event.getUserId();
        
        // 更新用户行为聚合
        updateUserBehaviorAggregate(event);
        
        // 更新用户画像
        updateUserProfile(event);
        
        // 检查是否需要生成推荐
        if (shouldGenerateRecommendation(context)) {
            generateRecommendation(userId, collector);
        }
    }
    
    /**
     * 处理商品创建事件
     */
    private void handleProductCreated(ProductCreated event) throws Exception {
        ProductAggregate aggregate = new ProductAggregate(
            event.getProductId(),
            event.getName(),
            event.getCategory(),
            event.getPrice(),
            event.getAttributes()
        );
        
        products.put(event.getProductId(), aggregate);
    }
    
    /**
     * 处理商品更新事件
     */
    private void handleProductUpdated(ProductUpdated event) throws Exception {
        ProductAggregate existing = products.get(event.getProductId());
        if (existing != null) {
            // 更新商品信息
            ProductAggregate updated = existing.updateField(event.getFieldName(), event.getNewValue());
            products.put(event.getProductId(), updated);
        }
    }
    
    /**
     * 更新用户行为聚合
     */
    private void updateUserBehaviorAggregate(UserBehaviorOccurred event) throws Exception {
        String behaviorKey = event.getProductId() + ":" + event.getActionType();
        UserBehaviorAggregate existing = userBehaviors.get(behaviorKey);
        
        if (existing == null) {
            existing = new UserBehaviorAggregate(
                event.getUserId(),
                event.getProductId(),
                event.getActionType(),
                1,
                event.getTimestamp()
            );
        } else {
            existing = existing.incrementCount().updateLastTime(event.getTimestamp());
        }
        
        userBehaviors.put(behaviorKey, existing);
    }
    
    /**
     * 更新用户画像
     */
    private void updateUserProfile(UserBehaviorOccurred event) throws Exception {
        UserProfileAggregate profile = userProfile.value();
        
        if (profile == null) {
            profile = new UserProfileAggregate(event.getUserId());
        }
        
        // 更新分类偏好
        profile = profile.updateCategoryPreference(
            getProductCategory(event.getProductId()),
            getActionWeight(event.getActionType())
        );
        
        // 更新购买历史
        if ("PURCHASE".equals(event.getActionType())) {
            profile = profile.addPurchasedProduct(event.getProductId());
        }
        
        userProfile.update(profile);
        
        // 发布用户画像更新事件
        // collector.collect(new UserProfileUpdated(...));
    }
    
    /**
     * 生成推荐
     */
    private void generateRecommendation(String userId, Collector<Event> collector) throws Exception {
        UserProfileAggregate profile = userProfile.value();
        if (profile == null) return;
        
        // 获取推荐模型
        RecommendationModel currentModel = getOrCreateModel();
        
        // 基于用户画像和商品信息生成推荐
        List<RecommendationGenerated.RecommendationItem> recommendations = 
            currentModel.generateRecommendations(profile, getAllProducts());
        
        // 创建推荐事件
        RecommendationGenerated recommendationEvent = new RecommendationGenerated(
            userId,
            recommendations,
            currentModel.getAlgorithmName(),
            currentModel.getVersion(),
            currentModel.getConfidence(),
            1L
        );
        
        collector.collect(recommendationEvent);
    }
    
    /**
     * 判断是否应该生成推荐
     */
    private boolean shouldGenerateRecommendation(Context context) {
        // 简化实现：基于时间间隔
        return context.timestamp() % RECOMMENDATION_INTERVAL == 0;
    }
    
    /**
     * 获取或创建推荐模型
     */
    private RecommendationModel getOrCreateModel() throws Exception {
        RecommendationModel currentModel = model.value();
        
        if (currentModel == null) {
            currentModel = new CollaborativeFilteringModel();
            model.update(currentModel);
        }
        
        return currentModel;
    }
    
    /**
     * 获取商品分类
     */
    private String getProductCategory(String productId) throws Exception {
        ProductAggregate product = products.get(productId);
        return product != null ? product.getCategory() : "unknown";
    }
    
    /**
     * 获取行为权重
     */
    private double getActionWeight(String actionType) {
        switch (actionType.toUpperCase()) {
            case "PURCHASE": return 5.0;
            case "ADD_TO_CART": return 3.0;
            case "CLICK": return 2.0;
            case "VIEW": return 1.0;
            default: return 0.5;
        }
    }
    
    /**
     * 获取所有商品
     */
    private List<ProductAggregate> getAllProducts() throws Exception {
        List<ProductAggregate> allProducts = new ArrayList<>();
        for (ProductAggregate product : products.values()) {
            allProducts.add(product);
        }
        return allProducts;
    }
}

/**
 * 用户行为聚合
 */
class UserBehaviorAggregate {
    private final String userId;
    private final String productId;
    private final String actionType;
    private final int count;
    private final java.time.Instant lastTime;
    
    public UserBehaviorAggregate(String userId, String productId, String actionType, 
                               int count, java.time.Instant lastTime) {
        this.userId = userId;
        this.productId = productId;
        this.actionType = actionType;
        this.count = count;
        this.lastTime = lastTime;
    }
    
    public UserBehaviorAggregate incrementCount() {
        return new UserBehaviorAggregate(userId, productId, actionType, count + 1, lastTime);
    }
    
    public UserBehaviorAggregate updateLastTime(java.time.Instant newTime) {
        return new UserBehaviorAggregate(userId, productId, actionType, count, newTime);
    }
    
    // Getters
    public String getUserId() { return userId; }
    public String getProductId() { return productId; }
    public String getActionType() { return actionType; }
    public int getCount() { return count; }
    public java.time.Instant getLastTime() { return lastTime; }
}

/**
 * 商品聚合
 */
class ProductAggregate {
    private final String productId;
    private final String name;
    private final String category;
    private final Double price;
    private final ProductCreated.ProductAttributes attributes;
    
    public ProductAggregate(String productId, String name, String category, 
                          Double price, ProductCreated.ProductAttributes attributes) {
        this.productId = productId;
        this.name = name;
        this.category = category;
        this.price = price;
        this.attributes = attributes;
    }
    
    public ProductAggregate updateField(String fieldName, Object newValue) {
        // 简化实现，实际应该根据字段名更新对应字段
        return this;
    }
    
    // Getters
    public String getProductId() { return productId; }
    public String getName() { return name; }
    public String getCategory() { return category; }
    public Double getPrice() { return price; }
    public ProductCreated.ProductAttributes getAttributes() { return attributes; }
}

/**
 * 用户画像聚合
 */
class UserProfileAggregate {
    private final String userId;
    private final Map<String, Double> categoryPreferences;
    private final Set<String> purchasedProducts;
    private final java.time.Instant lastUpdated;
    
    public UserProfileAggregate(String userId) {
        this.userId = userId;
        this.categoryPreferences = new HashMap<>();
        this.purchasedProducts = new HashSet<>();
        this.lastUpdated = java.time.Instant.now();
    }
    
    private UserProfileAggregate(String userId, Map<String, Double> categoryPreferences, 
                               Set<String> purchasedProducts, java.time.Instant lastUpdated) {
        this.userId = userId;
        this.categoryPreferences = new HashMap<>(categoryPreferences);
        this.purchasedProducts = new HashSet<>(purchasedProducts);
        this.lastUpdated = lastUpdated;
    }
    
    public UserProfileAggregate updateCategoryPreference(String category, double weight) {
        Map<String, Double> newPreferences = new HashMap<>(categoryPreferences);
        newPreferences.merge(category, weight, Double::sum);
        
        return new UserProfileAggregate(userId, newPreferences, purchasedProducts, java.time.Instant.now());
    }
    
    public UserProfileAggregate addPurchasedProduct(String productId) {
        Set<String> newPurchased = new HashSet<>(purchasedProducts);
        newPurchased.add(productId);
        
        return new UserProfileAggregate(userId, categoryPreferences, newPurchased, java.time.Instant.now());
    }
    
    // Getters
    public String getUserId() { return userId; }
    public Map<String, Double> getCategoryPreferences() { return categoryPreferences; }
    public Set<String> getPurchasedProducts() { return purchasedProducts; }
    public java.time.Instant getLastUpdated() { return lastUpdated; }
}

/**
 * 推荐模型接口
 */
interface RecommendationModel {
    List<RecommendationGenerated.RecommendationItem> generateRecommendations(
        UserProfileAggregate userProfile, 
        List<ProductAggregate> products
    );
    
    String getAlgorithmName();
    String getVersion();
    Double getConfidence();
}

/**
 * 协同过滤推荐模型
 */
class CollaborativeFilteringModel implements RecommendationModel {
    
    @Override
    public List<RecommendationGenerated.RecommendationItem> generateRecommendations(
            UserProfileAggregate userProfile, List<ProductAggregate> products) {
        
        // 获取用户最偏好的分类
        String preferredCategory = userProfile.getCategoryPreferences().entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("electronics");
        
        // 过滤同分类且未购买的商品
        return products.stream()
            .filter(p -> preferredCategory.equals(p.getCategory()))
            .filter(p -> !userProfile.getPurchasedProducts().contains(p.getProductId()))
            .limit(5)
            .map(p -> new RecommendationGenerated.RecommendationItem(
                p.getProductId(),
                0.8 + Math.random() * 0.2, // 模拟评分
                "基于分类偏好: " + preferredCategory
            ))
            .collect(Collectors.toList());
    }
    
    @Override
    public String getAlgorithmName() {
        return "EventDrivenCollaborativeFiltering";
    }
    
    @Override
    public String getVersion() {
        return "v1.0";
    }
    
    @Override
    public Double getConfidence() {
        return 0.85;
    }
}

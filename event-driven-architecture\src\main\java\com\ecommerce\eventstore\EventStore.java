package com.ecommerce.eventstore;

import com.ecommerce.events.Event;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 事件存储 - 基于Kafka的事件溯源实现
 */
public class EventStore {
    
    private final KafkaProducer<String, String> producer;
    private final KafkaConsumer<String, String> consumer;
    private final ObjectMapper objectMapper;
    private final String topicPrefix;
    
    // 内存快照缓存
    private final Map<String, AggregateSnapshot> snapshotCache = new ConcurrentHashMap<>();
    
    public EventStore(KafkaProducer<String, String> producer, 
                     KafkaConsumer<String, String> consumer,
                     String topicPrefix) {
        this.producer = producer;
        this.consumer = consumer;
        this.objectMapper = new ObjectMapper();
        this.topicPrefix = topicPrefix;
    }
    
    /**
     * 保存事件到事件流
     */
    public CompletableFuture<Void> saveEvent(Event event) {
        try {
            String topic = getTopicName(event.getAggregateId());
            String eventJson = objectMapper.writeValueAsString(event);
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                topic, 
                event.getAggregateId(), 
                eventJson
            );
            
            return CompletableFuture.supplyAsync(() -> {
                try {
                    producer.send(record).get();
                    updateSnapshot(event);
                    return null;
                } catch (Exception e) {
                    throw new RuntimeException("Failed to save event", e);
                }
            });
            
        } catch (Exception e) {
            return CompletableFuture.failedFuture(e);
        }
    }
    
    /**
     * 批量保存事件
     */
    public CompletableFuture<Void> saveEvents(List<Event> events) {
        List<CompletableFuture<Void>> futures = events.stream()
            .map(this::saveEvent)
            .collect(Collectors.toList());
            
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }
    
    /**
     * 获取聚合的所有事件
     */
    public List<Event> getEvents(String aggregateId) {
        return getEvents(aggregateId, 0L);
    }
    
    /**
     * 获取聚合从指定版本开始的事件
     */
    public List<Event> getEvents(String aggregateId, Long fromVersion) {
        try {
            String topic = getTopicName(aggregateId);
            List<Event> events = new ArrayList<>();
            
            // 订阅主题
            consumer.subscribe(Collections.singletonList(topic));
            
            // 消费事件
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(5));
            
            for (ConsumerRecord<String, String> record : records) {
                if (record.key().equals(aggregateId)) {
                    Event event = objectMapper.readValue(record.value(), Event.class);
                    if (event.getVersion() >= fromVersion) {
                        events.add(event);
                    }
                }
            }
            
            // 按版本排序
            events.sort(Comparator.comparing(Event::getVersion));
            
            return events;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to get events for aggregate: " + aggregateId, e);
        }
    }
    
    /**
     * 获取聚合的快照
     */
    public Optional<AggregateSnapshot> getSnapshot(String aggregateId) {
        return Optional.ofNullable(snapshotCache.get(aggregateId));
    }
    
    /**
     * 保存聚合快照
     */
    public void saveSnapshot(AggregateSnapshot snapshot) {
        snapshotCache.put(snapshot.getAggregateId(), snapshot);
        
        // 异步持久化快照到存储
        CompletableFuture.runAsync(() -> {
            try {
                String topic = getSnapshotTopicName(snapshot.getAggregateId());
                String snapshotJson = objectMapper.writeValueAsString(snapshot);
                
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    topic,
                    snapshot.getAggregateId(),
                    snapshotJson
                );
                
                producer.send(record);
                
            } catch (Exception e) {
                // 记录日志但不抛出异常，快照失败不应影响主流程
                System.err.println("Failed to save snapshot: " + e.getMessage());
            }
        });
    }
    
    /**
     * 事件重放 - 从事件流重建聚合状态
     */
    public <T> T replayEvents(String aggregateId, Class<T> aggregateClass) {
        try {
            // 先尝试从快照开始
            Optional<AggregateSnapshot> snapshot = getSnapshot(aggregateId);
            
            Long fromVersion = 0L;
            T aggregate = null;
            
            if (snapshot.isPresent()) {
                fromVersion = snapshot.get().getVersion() + 1;
                aggregate = objectMapper.readValue(snapshot.get().getData(), aggregateClass);
            } else {
                aggregate = aggregateClass.getDeclaredConstructor().newInstance();
            }
            
            // 重放事件
            List<Event> events = getEvents(aggregateId, fromVersion);
            
            for (Event event : events) {
                // 通过反射调用聚合的事件处理方法
                String methodName = "apply" + event.getEventType();
                try {
                    var method = aggregateClass.getDeclaredMethod(methodName, event.getClass());
                    method.setAccessible(true);
                    method.invoke(aggregate, event);
                } catch (NoSuchMethodException e) {
                    // 如果没有对应的处理方法，跳过该事件
                    System.out.println("No handler for event: " + event.getEventType());
                }
            }
            
            return aggregate;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to replay events for aggregate: " + aggregateId, e);
        }
    }
    
    /**
     * 事件流订阅
     */
    public EventSubscription subscribe(String pattern, EventHandler handler) {
        return new KafkaEventSubscription(consumer, pattern, handler, objectMapper);
    }
    
    /**
     * 更新内存快照
     */
    private void updateSnapshot(Event event) {
        String aggregateId = event.getAggregateId();
        AggregateSnapshot snapshot = snapshotCache.get(aggregateId);
        
        if (snapshot == null || event.getVersion() > snapshot.getVersion()) {
            // 创建新快照或更新现有快照
            // 这里简化处理，实际应该根据业务逻辑更新
            AggregateSnapshot newSnapshot = new AggregateSnapshot(
                aggregateId,
                event.getVersion(),
                event.getTimestamp(),
                "{}" // 简化的聚合状态
            );
            snapshotCache.put(aggregateId, newSnapshot);
        }
    }
    
    private String getTopicName(String aggregateId) {
        // 根据聚合ID的前缀确定主题名称
        String aggregateType = aggregateId.split("-")[0];
        return topicPrefix + "-" + aggregateType + "-events";
    }
    
    private String getSnapshotTopicName(String aggregateId) {
        String aggregateType = aggregateId.split("-")[0];
        return topicPrefix + "-" + aggregateType + "-snapshots";
    }
}

/**
 * 聚合快照
 */
class AggregateSnapshot {
    private final String aggregateId;
    private final Long version;
    private final java.time.Instant timestamp;
    private final String data;
    
    public AggregateSnapshot(String aggregateId, Long version, java.time.Instant timestamp, String data) {
        this.aggregateId = aggregateId;
        this.version = version;
        this.timestamp = timestamp;
        this.data = data;
    }
    
    // Getters
    public String getAggregateId() { return aggregateId; }
    public Long getVersion() { return version; }
    public java.time.Instant getTimestamp() { return timestamp; }
    public String getData() { return data; }
}

/**
 * 事件处理器接口
 */
@FunctionalInterface
interface EventHandler {
    void handle(Event event);
}

/**
 * 事件订阅
 */
interface EventSubscription {
    void start();
    void stop();
    boolean isRunning();
}

/**
 * Kafka事件订阅实现
 */
class KafkaEventSubscription implements EventSubscription {
    private final KafkaConsumer<String, String> consumer;
    private final String pattern;
    private final EventHandler handler;
    private final ObjectMapper objectMapper;
    private volatile boolean running = false;
    private Thread consumerThread;
    
    public KafkaEventSubscription(KafkaConsumer<String, String> consumer, 
                                 String pattern, 
                                 EventHandler handler,
                                 ObjectMapper objectMapper) {
        this.consumer = consumer;
        this.pattern = pattern;
        this.handler = handler;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public void start() {
        if (running) return;
        
        running = true;
        consumerThread = new Thread(() -> {
            consumer.subscribe(Collections.singletonList(pattern));
            
            while (running) {
                try {
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                    
                    for (ConsumerRecord<String, String> record : records) {
                        Event event = objectMapper.readValue(record.value(), Event.class);
                        handler.handle(event);
                    }
                    
                } catch (Exception e) {
                    System.err.println("Error processing events: " + e.getMessage());
                }
            }
        });
        
        consumerThread.start();
    }
    
    @Override
    public void stop() {
        running = false;
        if (consumerThread != null) {
            consumerThread.interrupt();
        }
    }
    
    @Override
    public boolean isRunning() {
        return running;
    }
}

package com.ecommerce.algorithm;

import com.ecommerce.model.Product;
import com.ecommerce.model.UserBehavior;
import com.ecommerce.model.Recommendation;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐引擎 - 基于用户行为的协同过滤推荐算法
 */
public class RecommendationEngine extends KeyedProcessFunction<String, UserBehavior, Recommendation> {
    
    // 用户行为历史状态
    private transient MapState<String, List<UserBehavior>> userBehaviorHistory;
    
    // 商品信息状态
    private transient MapState<String, Product> productInfo;
    
    // 用户偏好分类状态
    private transient MapState<String, Double> userCategoryPreferences;
    
    // 最后推荐时间状态
    private transient ValueState<Long> lastRecommendationTime;
    
    // 推荐间隔（毫秒）
    private static final long RECOMMENDATION_INTERVAL = 30000; // 30秒
    
    // 推荐商品数量
    private static final int RECOMMENDATION_COUNT = 5;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        MapStateDescriptor<String, List<UserBehavior>> behaviorDescriptor = 
            new MapStateDescriptor<>("userBehaviorHistory", 
                TypeInformation.of(String.class),
                TypeInformation.of(new TypeHint<List<UserBehavior>>() {}));
        userBehaviorHistory = getRuntimeContext().getMapState(behaviorDescriptor);
        
        MapStateDescriptor<String, Product> productDescriptor = 
            new MapStateDescriptor<>("productInfo",
                TypeInformation.of(String.class),
                TypeInformation.of(Product.class));
        productInfo = getRuntimeContext().getMapState(productDescriptor);
        
        MapStateDescriptor<String, Double> preferenceDescriptor = 
            new MapStateDescriptor<>("userCategoryPreferences",
                TypeInformation.of(String.class),
                TypeInformation.of(Double.class));
        userCategoryPreferences = getRuntimeContext().getMapState(preferenceDescriptor);
        
        ValueStateDescriptor<Long> timeDescriptor = 
            new ValueStateDescriptor<>("lastRecommendationTime", Long.class);
        lastRecommendationTime = getRuntimeContext().getState(timeDescriptor);
    }

    @Override
    public void processElement(UserBehavior behavior, Context context, Collector<Recommendation> collector) throws Exception {
        String userId = behavior.getUserId();
        
        // 更新用户行为历史
        updateUserBehaviorHistory(userId, behavior);
        
        // 更新用户分类偏好
        updateUserCategoryPreferences(behavior);
        
        // 检查是否需要生成推荐
        Long lastTime = lastRecommendationTime.value();
        long currentTime = System.currentTimeMillis();
        
        if (lastTime == null || (currentTime - lastTime) > RECOMMENDATION_INTERVAL) {
            // 生成推荐
            Recommendation recommendation = generateRecommendation(userId);
            if (recommendation != null) {
                collector.collect(recommendation);
                lastRecommendationTime.update(currentTime);
            }
        }
    }

    private void updateUserBehaviorHistory(String userId, UserBehavior behavior) throws Exception {
        List<UserBehavior> history = userBehaviorHistory.get(userId);
        if (history == null) {
            history = new ArrayList<>();
        }
        
        history.add(behavior);
        
        // 保持最近100条记录
        if (history.size() > 100) {
            history = history.subList(history.size() - 100, history.size());
        }
        
        userBehaviorHistory.put(userId, history);
    }

    private void updateUserCategoryPreferences(UserBehavior behavior) throws Exception {
        String category = behavior.getCategory();
        if (category != null) {
            Double currentScore = userCategoryPreferences.get(category);
            if (currentScore == null) {
                currentScore = 0.0;
            }
            
            // 根据行为类型给予不同权重
            double weight = getActionWeight(behavior.getActionType());
            userCategoryPreferences.put(category, currentScore + weight);
        }
    }

    private double getActionWeight(String actionType) {
        switch (actionType.toLowerCase()) {
            case "purchase":
                return 5.0;
            case "add_to_cart":
                return 3.0;
            case "click":
                return 2.0;
            case "view":
                return 1.0;
            default:
                return 0.5;
        }
    }

    private Recommendation generateRecommendation(String userId) throws Exception {
        // 获取用户历史行为
        List<UserBehavior> history = userBehaviorHistory.get(userId);
        if (history == null || history.isEmpty()) {
            return null;
        }
        
        // 基于分类偏好推荐
        List<String> recommendedProducts = recommendBasedOnCategoryPreference(userId);
        
        if (recommendedProducts.isEmpty()) {
            // 如果基于分类的推荐为空，使用热门商品推荐
            recommendedProducts = getPopularProducts();
        }
        
        // 创建推荐结果
        return new Recommendation(
            userId,
            recommendedProducts,
            "CategoryBasedCollaborativeFiltering",
            0.85,
            "基于用户分类偏好和协同过滤的推荐"
        );
    }

    private List<String> recommendBasedOnCategoryPreference(String userId) throws Exception {
        // 获取用户最喜欢的分类
        String preferredCategory = getPreferredCategory();
        
        if (preferredCategory == null) {
            return new ArrayList<>();
        }
        
        // 获取该分类下的商品（这里简化处理，实际应该从商品库中查询）
        List<String> categoryProducts = getCategoryProducts(preferredCategory);
        
        // 过滤掉用户已经购买的商品
        Set<String> purchasedProducts = getPurchasedProducts(userId);
        
        return categoryProducts.stream()
            .filter(productId -> !purchasedProducts.contains(productId))
            .limit(RECOMMENDATION_COUNT)
            .collect(Collectors.toList());
    }

    private String getPreferredCategory() throws Exception {
        String maxCategory = null;
        double maxScore = 0.0;
        
        for (Map.Entry<String, Double> entry : userCategoryPreferences.entries()) {
            if (entry.getValue() > maxScore) {
                maxScore = entry.getValue();
                maxCategory = entry.getKey();
            }
        }
        
        return maxCategory;
    }

    private List<String> getCategoryProducts(String category) {
        // 模拟分类商品数据
        Map<String, List<String>> categoryProductMap = new HashMap<>();
        categoryProductMap.put("electronics", Arrays.asList("phone001", "laptop002", "tablet003", "camera004", "headphone005"));
        categoryProductMap.put("clothing", Arrays.asList("shirt001", "pants002", "dress003", "shoes004", "jacket005"));
        categoryProductMap.put("books", Arrays.asList("book001", "book002", "book003", "book004", "book005"));
        categoryProductMap.put("home", Arrays.asList("sofa001", "table002", "lamp003", "carpet004", "curtain005"));
        
        return categoryProductMap.getOrDefault(category.toLowerCase(), new ArrayList<>());
    }

    private Set<String> getPurchasedProducts(String userId) throws Exception {
        List<UserBehavior> history = userBehaviorHistory.get(userId);
        if (history == null) {
            return new HashSet<>();
        }
        
        return history.stream()
            .filter(behavior -> "purchase".equals(behavior.getActionType()))
            .map(UserBehavior::getProductId)
            .collect(Collectors.toSet());
    }

    private List<String> getPopularProducts() {
        // 返回默认的热门商品
        return Arrays.asList("popular001", "popular002", "popular003", "popular004", "popular005");
    }
}

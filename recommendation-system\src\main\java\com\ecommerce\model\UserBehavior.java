package com.ecommerce.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.Objects;

/**
 * 用户行为模型
 */
public class UserBehavior implements Serializable {
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("productId")
    private String productId;
    
    @JsonProperty("actionType")
    private String actionType; // view, click, purchase, add_to_cart
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("sessionId")
    private String sessionId;
    
    @JsonProperty("category")
    private String category;

    // 默认构造函数
    public UserBehavior() {}

    // 带参数构造函数
    public UserBehavior(String userId, String productId, String actionType, String category) {
        this.userId = userId;
        this.productId = productId;
        this.actionType = actionType;
        this.category = category;
        this.timestamp = System.currentTimeMillis();
        this.sessionId = generateSessionId(userId);
    }

    private String generateSessionId(String userId) {
        return userId + "_" + (System.currentTimeMillis() / 1000 / 3600); // 按小时分组
    }

    // Getter和Setter方法
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserBehavior that = (UserBehavior) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(productId, that.productId) &&
                Objects.equals(timestamp, that.timestamp);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, productId, timestamp);
    }

    @Override
    public String toString() {
        return "UserBehavior{" +
                "userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", actionType='" + actionType + '\'' +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                ", category='" + category + '\'' +
                '}';
    }
}

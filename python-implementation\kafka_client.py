#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kafka客户端封装
"""

import json
import time
import threading
from typing import Callable, Any, Optional
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KafkaProducerClient:
    """Kafka生产者客户端"""
    
    def __init__(self, bootstrap_servers: str = 'localhost:9092'):
        self.bootstrap_servers = bootstrap_servers
        self.producer = None
        self.message_count = 0
        self._init_producer()
    
    def _init_producer(self):
        """初始化生产者"""
        try:
            self.producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None,
                acks='all',
                retries=3,
                batch_size=16384,
                linger_ms=1,
                buffer_memory=33554432
            )
            logger.info(f"Kafka生产者初始化成功: {self.bootstrap_servers}")
        except Exception as e:
            logger.error(f"Kafka生产者初始化失败: {e}")
            self.producer = None
    
    def send_message(self, topic: str, message: Any, key: Optional[str] = None) -> bool:
        """发送消息"""
        if not self.producer:
            logger.error("Kafka生产者未初始化")
            return False
        
        try:
            # 如果message是对象，转换为字典
            if hasattr(message, 'to_json'):
                message_data = json.loads(message.to_json())
            elif hasattr(message, '__dict__'):
                message_data = message.__dict__
            else:
                message_data = message
            
            future = self.producer.send(topic, value=message_data, key=key)
            record_metadata = future.get(timeout=10)
            
            self.message_count += 1
            logger.info(f"消息发送成功: Topic={record_metadata.topic}, "
                       f"Partition={record_metadata.partition}, "
                       f"Offset={record_metadata.offset}")
            return True
            
        except KafkaError as e:
            logger.error(f"Kafka发送消息失败: {e}")
            return False
        except Exception as e:
            logger.error(f"发送消息时发生错误: {e}")
            return False
    
    def get_message_count(self) -> int:
        """获取发送消息数量"""
        return self.message_count
    
    def close(self):
        """关闭生产者"""
        if self.producer:
            self.producer.close()
            logger.info("Kafka生产者已关闭")


class KafkaConsumerClient:
    """Kafka消费者客户端"""
    
    def __init__(self, topics: list, group_id: str, bootstrap_servers: str = 'localhost:9092'):
        self.topics = topics
        self.group_id = group_id
        self.bootstrap_servers = bootstrap_servers
        self.consumer = None
        self.running = False
        self.message_count = 0
        self._init_consumer()
    
    def _init_consumer(self):
        """初始化消费者"""
        try:
            self.consumer = KafkaConsumer(
                *self.topics,
                bootstrap_servers=self.bootstrap_servers,
                group_id=self.group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                key_deserializer=lambda k: k.decode('utf-8') if k else None,
                auto_offset_reset='latest',
                enable_auto_commit=True,
                auto_commit_interval_ms=1000,
                session_timeout_ms=30000
            )
            logger.info(f"Kafka消费者初始化成功: Topics={self.topics}, Group={self.group_id}")
        except Exception as e:
            logger.error(f"Kafka消费者初始化失败: {e}")
            self.consumer = None
    
    def start_consuming(self, message_handler: Callable[[str, Any], None]):
        """开始消费消息"""
        if not self.consumer:
            logger.error("Kafka消费者未初始化")
            return
        
        self.running = True
        logger.info(f"开始消费消息: {self.topics}")
        
        try:
            while self.running:
                message_batch = self.consumer.poll(timeout_ms=1000)
                
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        try:
                            self.message_count += 1
                            message_handler(message.topic, message.value)
                        except Exception as e:
                            logger.error(f"处理消息时发生错误: {e}")
                            
        except Exception as e:
            logger.error(f"消费消息时发生错误: {e}")
        finally:
            logger.info("消息消费已停止")
    
    def stop_consuming(self):
        """停止消费消息"""
        self.running = False
    
    def get_message_count(self) -> int:
        """获取消费消息数量"""
        return self.message_count
    
    def close(self):
        """关闭消费者"""
        self.stop_consuming()
        if self.consumer:
            self.consumer.close()
            logger.info("Kafka消费者已关闭")


class MockKafkaClient:
    """模拟Kafka客户端（用于没有Kafka环境的情况）"""
    
    def __init__(self):
        self.topics = {}
        self.consumers = {}
        self.message_count = 0
        logger.info("使用模拟Kafka客户端")
    
    def send_message(self, topic: str, message: Any, key: Optional[str] = None) -> bool:
        """模拟发送消息"""
        if topic not in self.topics:
            self.topics[topic] = []
        
        # 转换消息格式
        if hasattr(message, 'to_json'):
            message_data = json.loads(message.to_json())
        elif hasattr(message, '__dict__'):
            message_data = message.__dict__
        else:
            message_data = message
        
        self.topics[topic].append({
            'key': key,
            'value': message_data,
            'timestamp': int(time.time() * 1000)
        })
        
        self.message_count += 1
        logger.info(f"模拟消息发送: Topic={topic}, Key={key}")
        
        # 通知消费者
        if topic in self.consumers:
            for consumer in self.consumers[topic]:
                try:
                    consumer(topic, message_data)
                except Exception as e:
                    logger.error(f"通知消费者时发生错误: {e}")
        
        return True
    
    def register_consumer(self, topic: str, handler: Callable[[str, Any], None]):
        """注册消费者"""
        if topic not in self.consumers:
            self.consumers[topic] = []
        self.consumers[topic].append(handler)
        logger.info(f"注册消费者: Topic={topic}")
    
    def get_message_count(self) -> int:
        """获取消息数量"""
        return self.message_count
    
    def get_topic_messages(self, topic: str) -> list:
        """获取主题的所有消息"""
        return self.topics.get(topic, [])
    
    def close(self):
        """关闭客户端"""
        logger.info("模拟Kafka客户端已关闭")

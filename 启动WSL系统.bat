@echo off
chcp 65001 >nul
title 分布式实时电商推荐系统 - WSL启动器

echo ========================================
echo   分布式实时电商推荐系统 - WSL启动器
echo ========================================
echo.

echo 正在启动WSL环境...
echo.

REM 检查WSL是否可用
wsl --status >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] WSL未安装或未启动
    echo 请确保WSL已正确安装并启动
    pause
    exit /b 1
)

echo [信息] WSL环境检查通过
echo.

echo 可用操作：
echo 1. 进入WSL交互式环境
echo 2. 运行WSL启动脚本
echo 3. 直接启动完整系统
echo 4. 查看系统状态
echo 5. 打开Flink Web UI
echo 6. 退出
echo.

set /p choice="请选择操作 (1-6): "

if "%choice%"=="1" (
    echo.
    echo [信息] 进入WSL交互式环境...
    echo [提示] 在WSL中执行: cd /mnt/c/Users/<USER>/Desktop/first
    echo [提示] 然后运行: ./wsl-start.sh
    echo.
    wsl
) else if "%choice%"=="2" (
    echo.
    echo [信息] 运行WSL启动脚本...
    wsl bash -c "cd /mnt/c/Users/<USER>/Desktop/first && ./wsl-start.sh"
) else if "%choice%"=="3" (
    echo.
    echo [信息] 直接启动完整系统...
    wsl bash -c "cd /mnt/c/Users/<USER>/Desktop/first && ./build-and-run.sh deploy"
) else if "%choice%"=="4" (
    echo.
    echo [信息] 查看系统状态...
    wsl bash -c "cd /mnt/c/Users/<USER>/Desktop/first && ./build-and-run.sh status"
    pause
) else if "%choice%"=="5" (
    echo.
    echo [信息] 打开Flink Web UI...
    echo Flink Web UI地址: http://localhost:8081
    start http://localhost:8081
    echo.
    echo [提示] 如果页面无法打开，请确保Flink集群已启动
    pause
) else if "%choice%"=="6" (
    echo.
    echo [信息] 退出启动器
    exit /b 0
) else (
    echo.
    echo [错误] 无效选择，请重新运行
    pause
    exit /b 1
)

echo.
echo 操作完成
pause

#!/bin/bash
# Kafka集群管理脚本

KAFKA_HOME="./kafka_2.13-3.6.0"
CONFIG_DIR="./kafka-cluster/config"

function start_zookeeper() {
    echo "启动Zookeeper..."
    $KAFKA_HOME/bin/zookeeper-server-start.sh -daemon $CONFIG_DIR/zookeeper.properties
    sleep 5
    echo "Zookeeper已启动"
}

function start_kafka_brokers() {
    echo "启动Kafka Brokers..."
    
    # 启动Hadoop01 Broker
    echo "启动Hadoop01 Broker..."
    $KAFKA_HOME/bin/kafka-server-start.sh -daemon $CONFIG_DIR/server-hadoop01.properties
    sleep 3
    
    # 启动Hadoop02 Broker
    echo "启动Hadoop02 Broker..."
    $KAFKA_HOME/bin/kafka-server-start.sh -daemon $CONFIG_DIR/server-hadoop02.properties
    sleep 3
    
    # 启动Hadoop03 Broker
    echo "启动Hadoop03 Broker..."
    $KAFKA_HOME/bin/kafka-server-start.sh -daemon $CONFIG_DIR/server-hadoop03.properties
    sleep 3
    
    echo "所有Kafka Brokers已启动"
}

function create_topics() {
    echo "创建Kafka Topics..."
    
    # 创建用户行为数据Topic
    $KAFKA_HOME/bin/kafka-topics.sh --create \
        --topic user-behavior \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3
    
    # 创建商品信息Topic
    $KAFKA_HOME/bin/kafka-topics.sh --create \
        --topic product-info \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3
    
    # 创建推荐结果Topic
    $KAFKA_HOME/bin/kafka-topics.sh --create \
        --topic recommendation-results \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3
    
    echo "Topics创建完成"
}

function list_topics() {
    echo "当前Topics列表："
    $KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server localhost:9092
}

function describe_topics() {
    echo "Topics详细信息："
    $KAFKA_HOME/bin/kafka-topics.sh --describe --bootstrap-server localhost:9092
}

function stop_kafka() {
    echo "停止Kafka集群..."
    $KAFKA_HOME/bin/kafka-server-stop.sh
    $KAFKA_HOME/bin/zookeeper-server-stop.sh
    echo "Kafka集群已停止"
}

function test_kafka() {
    echo "测试Kafka集群..."
    
    # 测试生产者
    echo "测试消息: Hello Kafka" | $KAFKA_HOME/bin/kafka-console-producer.sh \
        --topic user-behavior \
        --bootstrap-server localhost:9092
    
    # 测试消费者（后台运行）
    timeout 5 $KAFKA_HOME/bin/kafka-console-consumer.sh \
        --topic user-behavior \
        --bootstrap-server localhost:9092 \
        --from-beginning
}

case "$1" in
    start)
        start_zookeeper
        start_kafka_brokers
        sleep 5
        create_topics
        ;;
    stop)
        stop_kafka
        ;;
    topics)
        list_topics
        ;;
    describe)
        describe_topics
        ;;
    test)
        test_kafka
        ;;
    *)
        echo "用法: $0 {start|stop|topics|describe|test}"
        echo "  start    - 启动Kafka集群并创建Topics"
        echo "  stop     - 停止Kafka集群"
        echo "  topics   - 列出所有Topics"
        echo "  describe - 显示Topics详细信息"
        echo "  test     - 测试Kafka集群"
        exit 1
        ;;
esac

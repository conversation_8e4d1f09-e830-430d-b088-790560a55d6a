package com.ecommerce.recommendation;

import com.ecommerce.algorithm.RecommendationEngine;
import com.ecommerce.model.Product;
import com.ecommerce.model.UserBehavior;
import com.ecommerce.model.Recommendation;
import com.ecommerce.serialization.JsonDeserializationSchema;
import com.ecommerce.serialization.JsonSerializationSchema;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.PrintSinkFunction;

/**
 * 实时推荐系统主作业
 */
public class RecommendationJob {
    
    private static final String KAFKA_BROKERS = "localhost:9092";
    private static final String USER_BEHAVIOR_TOPIC = "user-behavior";
    private static final String PRODUCT_INFO_TOPIC = "product-info";
    private static final String RECOMMENDATION_TOPIC = "recommendation-results";
    private static final String CONSUMER_GROUP = "recommendation-consumer-group";

    public static void main(String[] args) throws Exception {
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(3);
        
        // 启用检查点
        env.enableCheckpointing(60000); // 每分钟检查点
        
        System.out.println("启动实时推荐系统...");
        
        // 1. 创建用户行为数据源
        KafkaSource<UserBehavior> userBehaviorSource = KafkaSource.<UserBehavior>builder()
            .setBootstrapServers(KAFKA_BROKERS)
            .setTopics(USER_BEHAVIOR_TOPIC)
            .setGroupId(CONSUMER_GROUP + "-behavior")
            .setStartingOffsets(OffsetsInitializer.latest())
            .setValueOnlyDeserializer(new JsonDeserializationSchema<>(UserBehavior.class))
            .build();
        
        // 2. 创建商品信息数据源
        KafkaSource<Product> productSource = KafkaSource.<Product>builder()
            .setBootstrapServers(KAFKA_BROKERS)
            .setTopics(PRODUCT_INFO_TOPIC)
            .setGroupId(CONSUMER_GROUP + "-product")
            .setStartingOffsets(OffsetsInitializer.latest())
            .setValueOnlyDeserializer(new JsonDeserializationSchema<>(Product.class))
            .build();
        
        // 3. 读取数据流
        DataStream<UserBehavior> userBehaviorStream = env
            .fromSource(userBehaviorSource, WatermarkStrategy.noWatermarks(), "User Behavior Source")
            .name("用户行为数据流");
        
        DataStream<Product> productStream = env
            .fromSource(productSource, WatermarkStrategy.noWatermarks(), "Product Source")
            .name("商品信息数据流");
        
        // 4. 处理用户行为数据并生成推荐
        DataStream<Recommendation> recommendationStream = userBehaviorStream
            .keyBy(UserBehavior::getUserId)
            .process(new RecommendationEngine())
            .name("推荐引擎处理");
        
        // 5. 输出推荐结果到控制台（用于调试）
        recommendationStream.addSink(new PrintSinkFunction<>("推荐结果"))
            .name("控制台输出");
        
        // 6. 将推荐结果发送到Kafka
        KafkaSink<Recommendation> recommendationSink = KafkaSink.<Recommendation>builder()
            .setBootstrapServers(KAFKA_BROKERS)
            .setRecordSerializer(new JsonSerializationSchema<>(RECOMMENDATION_TOPIC))
            .build();
        
        recommendationStream.sinkTo(recommendationSink)
            .name("推荐结果Kafka输出");
        
        // 7. 处理商品信息（用于更新商品库）
        productStream.addSink(new PrintSinkFunction<>("商品信息"))
            .name("商品信息处理");
        
        // 8. 执行作业
        env.execute("实时电商推荐系统");
    }
}

#!/bin/bash
# WSL环境启动脚本

echo "=== 分布式实时电商推荐系统 - WSL启动脚本 ==="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_step() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

function print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

function print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

function print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# 检查当前目录
print_step "环境检查"
CURRENT_DIR=$(pwd)
echo "当前目录: $CURRENT_DIR"

if [[ "$CURRENT_DIR" == *"/mnt/c/Users/<USER>/Desktop/first"* ]]; then
    print_success "已在正确的项目目录中"
else
    print_warning "请确保在项目目录中运行此脚本"
    echo "正确路径: /mnt/c/Users/<USER>/Desktop/first"
fi

# 检查Java环境
print_step "Java环境检查"
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    print_success "Java环境: $JAVA_VERSION"
else
    print_error "Java未安装或未配置"
    exit 1
fi

# 检查Maven环境
print_step "Maven环境检查"
if command -v mvn &> /dev/null; then
    MVN_VERSION=$(mvn -version 2>&1 | head -n 1)
    print_success "Maven环境: $MVN_VERSION"
else
    print_error "Maven未安装或未配置"
    exit 1
fi

# 检查项目文件
print_step "项目文件检查"
if [ -f "recommendation-system/target/recommendation-system-1.0-SNAPSHOT.jar" ]; then
    print_success "推荐系统JAR文件存在"
else
    print_warning "推荐系统JAR文件不存在，需要重新构建"
fi

if [ -f "message-source/target/message-source-1.0-SNAPSHOT.jar" ]; then
    print_success "消息源软件JAR文件存在"
else
    print_warning "消息源软件JAR文件不存在，需要重新构建"
fi

# 设置脚本权限
print_step "设置脚本权限"
chmod +x *.sh
print_success "脚本权限设置完成"

# 显示菜单
function show_menu() {
    echo ""
    print_step "请选择操作"
    echo "1. 🔧 重新构建项目"
    echo "2. 🚀 启动完整系统"
    echo "3. 📊 查看系统状态"
    echo "4. 🧪 运行演示脚本"
    echo "5. 💻 启动消息源软件"
    echo "6. 🌐 打开Flink Web UI"
    echo "7. 📝 查看使用指南"
    echo "8. 🛑 停止系统"
    echo "9. 🚪 退出"
    echo ""
    echo -n "请输入选择 (1-9): "
}

# 重新构建项目
function rebuild_project() {
    print_step "重新构建项目"
    
    print_warning "构建推荐系统..."
    cd recommendation-system
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        print_success "推荐系统构建成功"
    else
        print_error "推荐系统构建失败"
        cd ..
        return 1
    fi
    cd ..
    
    print_warning "构建消息源软件..."
    cd message-source
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        print_success "消息源软件构建成功"
    else
        print_error "消息源软件构建失败"
        cd ..
        return 1
    fi
    cd ..
    
    print_success "项目构建完成"
}

# 启动完整系统
function start_system() {
    print_step "启动完整系统"
    ./build-and-run.sh deploy
}

# 查看系统状态
function check_status() {
    print_step "系统状态检查"
    ./build-and-run.sh status
}

# 运行演示脚本
function run_demo() {
    print_step "运行演示脚本"
    ./demo-run.sh
}

# 启动消息源软件
function start_message_source() {
    print_step "启动消息源软件"
    if [ -f "message-source/target/message-source-1.0-SNAPSHOT.jar" ]; then
        java -jar message-source/target/message-source-1.0-SNAPSHOT.jar
    else
        print_error "消息源软件JAR文件不存在，请先构建项目"
    fi
}

# 打开Flink Web UI
function open_flink_ui() {
    print_step "Flink Web UI信息"
    echo "Flink Web UI地址: http://localhost:8081"
    echo "请在Windows浏览器中打开上述地址"
    echo ""
    echo "如果无法访问，请检查："
    echo "1. Flink集群是否已启动"
    echo "2. 端口8081是否被占用"
    echo "3. WSL网络配置是否正确"
}

# 查看使用指南
function show_guide() {
    print_step "使用指南"
    if [ -f "WSL使用指南.md" ]; then
        echo "详细使用指南请查看: WSL使用指南.md"
        echo ""
        echo "快速命令参考："
        echo "• 构建项目: ./wsl-start.sh 然后选择1"
        echo "• 启动系统: ./build-and-run.sh deploy"
        echo "• 查看状态: ./build-and-run.sh status"
        echo "• 停止系统: ./build-and-run.sh stop"
        echo ""
    else
        print_error "使用指南文件不存在"
    fi
}

# 停止系统
function stop_system() {
    print_step "停止系统"
    ./build-and-run.sh stop
}

# 主循环
while true; do
    show_menu
    read choice
    
    case $choice in
        1)
            rebuild_project
            ;;
        2)
            start_system
            ;;
        3)
            check_status
            ;;
        4)
            run_demo
            ;;
        5)
            start_message_source
            ;;
        6)
            open_flink_ui
            ;;
        7)
            show_guide
            ;;
        8)
            stop_system
            ;;
        9)
            print_success "退出WSL启动脚本"
            exit 0
            ;;
        *)
            print_error "无效选择，请输入1-9"
            ;;
    esac
    
    echo ""
    echo "按回车键继续..."
    read
done

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义
"""

import json
import time
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class Product:
    """商品信息模型"""
    product_id: str
    product_name: str
    category: str
    price: float
    description: str
    timestamp: int = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = int(time.time() * 1000)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(asdict(self), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Product':
        """从JSON字符串创建对象"""
        data = json.loads(json_str)
        return cls(**data)
    
    def __str__(self):
        return f"Product(id={self.product_id}, name={self.product_name}, category={self.category}, price={self.price})"


@dataclass
class UserBehavior:
    """用户行为模型"""
    user_id: str
    product_id: str
    action_type: str  # view, click, add_to_cart, purchase
    category: str
    timestamp: int = None
    session_id: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = int(time.time() * 1000)
        if self.session_id is None:
            self.session_id = f"{self.user_id}_{int(self.timestamp / 1000 / 3600)}"
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(asdict(self), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'UserBehavior':
        """从JSON字符串创建对象"""
        data = json.loads(json_str)
        return cls(**data)
    
    def __str__(self):
        return f"UserBehavior(user={self.user_id}, product={self.product_id}, action={self.action_type})"


@dataclass
class Recommendation:
    """推荐结果模型"""
    user_id: str
    recommended_products: List[str]
    algorithm: str
    confidence: float
    reason: str
    timestamp: int = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = int(time.time() * 1000)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(asdict(self), ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Recommendation':
        """从JSON字符串创建对象"""
        data = json.loads(json_str)
        return cls(**data)
    
    def __str__(self):
        return f"Recommendation(user={self.user_id}, products={self.recommended_products}, confidence={self.confidence})"


class UserProfile:
    """用户画像"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.behavior_history: List[UserBehavior] = []
        self.category_preferences: Dict[str, float] = {}
        self.purchased_products: set = set()
        self.last_activity_time = 0
        
    def add_behavior(self, behavior: UserBehavior):
        """添加用户行为"""
        self.behavior_history.append(behavior)
        self.last_activity_time = behavior.timestamp
        
        # 更新分类偏好
        category = behavior.category
        weight = self._get_action_weight(behavior.action_type)
        
        if category in self.category_preferences:
            self.category_preferences[category] += weight
        else:
            self.category_preferences[category] = weight
            
        # 记录购买的商品
        if behavior.action_type == 'purchase':
            self.purchased_products.add(behavior.product_id)
            
        # 保持最近100条记录
        if len(self.behavior_history) > 100:
            self.behavior_history = self.behavior_history[-100:]
    
    def _get_action_weight(self, action_type: str) -> float:
        """获取行为权重"""
        weights = {
            'purchase': 5.0,
            'add_to_cart': 3.0,
            'click': 2.0,
            'view': 1.0
        }
        return weights.get(action_type.lower(), 0.5)
    
    def get_preferred_category(self) -> str:
        """获取最偏好的分类"""
        if not self.category_preferences:
            return None
        return max(self.category_preferences.items(), key=lambda x: x[1])[0]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'behavior_count': len(self.behavior_history),
            'category_preferences': self.category_preferences,
            'purchased_products': list(self.purchased_products),
            'last_activity_time': self.last_activity_time,
            'preferred_category': self.get_preferred_category()
        }

# Flink集群配置

# JobManager配置
jobmanager.rpc.address: hadoop01
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m
jobmanager.bind-host: 0.0.0.0

# TaskManager配置
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 2
taskmanager.bind-host: 0.0.0.0

# 高可用配置
high-availability: zookeeper
high-availability.zookeeper.quorum: hadoop01:2181,hadoop02:2181,hadoop03:2181
high-availability.zookeeper.path.root: /flink
high-availability.cluster-id: /default_ns
high-availability.storageDir: file:///tmp/flink/ha/

# 检查点配置
state.backend: filesystem
state.checkpoints.dir: file:///tmp/flink/checkpoints
state.savepoints.dir: file:///tmp/flink/savepoints

# 网络配置
rest.port: 8081
rest.bind-address: 0.0.0.0

# 并行度配置
parallelism.default: 3

# 重启策略
restart-strategy: fixed-delay
restart-strategy.fixed-delay.attempts: 3
restart-strategy.fixed-delay.delay: 10 s

# 日志配置
env.log.dir: ./flink-cluster/logs
